package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 公益慈善类型枚举
 *
 */
@Getter
public enum CharityTypeEnum implements IBaseEnum<String> {
    CONTRIBUTION("CONTRIBUTION", "公益慈善贡献");

    private final String value;

    private final String label;

    CharityTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
