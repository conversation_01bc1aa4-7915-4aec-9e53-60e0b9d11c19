package com.youlai.boot.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 考核规则类型枚举（二级枚举）
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
public enum AssessmentRuleTypeEnum implements IBaseEnum<String> {

    // ========== 一级枚举：会议类型 ==========
    /**
     * 参加会议（一级分类）
     */
    MEETING("MEETING", "参加会议"),
    
    /**
     * 参加会员（代表）大会
     */
    MEETING_MEMBER("MEETING:MEMBER", "参加会员（代表）大会"),
    
    /**
     * 参加会长会议
     */
    MEETING_PRESIDENT("MEETING:PRESIDENT", "参加会长会议"),
    
    /**
     * 参加理事会会议
     */
    MEETING_BOARD("MEETING:BOARD", "参加理事会会议"),

    // ========== 一级枚举：活动类型 ==========
    /**
     * 参加活动（一级分类）
     */
    ACTIVITY("ACTIVITY", "参加活动"),
    
    /**
     * 参加调研、视察、考察等活动
     */
    ACTIVITY_RESEARCH("ACTIVITY:RESEARCH", "参加调研、视察、考察等活动"),
    
    /**
     * 参加行风评议、特约监督及营商环境等工作会议活动
     */
    ACTIVITY_SUPERVISION("ACTIVITY:SUPERVISION", "参加行风评议、特约监督及营商环境等工作会议活动"),
    
    /**
     * 参加培训活动
     */
    ACTIVITY_TRAINING("ACTIVITY:TRAINING", "参加培训活动"),
    
    /**
     * 参加相关会议与团体活动
     */
    ACTIVITY_MEETING("ACTIVITY:MEETING", "参加相关会议与团体活动"),

    // ========== 一级枚举：营商环境问题报送 ==========
    /**
     * 营商环境问题报送（一级分类）
     */
    ENVIRONMENT("ENVIRONMENT", "营商环境问题报送"),
    
    /**
     * 提交营商环境问题
     */
    ENVIRONMENT_SUBMIT("ENVIRONMENT:SUBMIT", "提交营商环境问题"),
    
    /**
     * 营商环境问题被采纳
     */
    ENVIRONMENT_ADOPTED("ENVIRONMENT:ADOPTED", "营商环境问题被采纳"),
    
    /**
     * 得到区领导批示
     */
    ENVIRONMENT_INSTRUCTED_DISTRICT("ENVIRONMENT:INSTRUCTED_DISTRICT", "得到区领导批示"),
    
    /**
     * 得到市领导批示
     */
    ENVIRONMENT_INSTRUCTED_CITY("ENVIRONMENT:INSTRUCTED_CITY", "得到市领导批示"),

    // ========== 一级枚举：意见建议 ==========
    /**
     * 意见征集汇总报送（一级分类）
     */
    OPINION("OPINION", "意见征集汇总报送"),
    
    /**
     * 提交意见建议
     */
    OPINION_SUBMIT("OPINION:SUBMIT", "提交意见建议"),
    
    /**
     * 意见建议被采纳
     */
    OPINION_ADOPTED("OPINION:ADOPTED", "意见建议被采纳"),

    // ========== 一级枚举：年度重点工作 ==========
    /**
     * 参加年度重点工作（一级分类）
     */
    KEY_WORK("KEY_WORK", "参加年度重点工作"),
    
    /**
     * 助力项目建设服务，参加引进外资活动
     */
    KEY_WORK_PROJECT_SERVICE("KEY_WORK:PROJECT_SERVICE", "助力项目建设服务，参加引进外资活动"),
    
    /**
     * 助推创一流营商环境
     */
    KEY_WORK_BUSINESS_ENVIRONMENT("KEY_WORK:BUSINESS_ENVIRONMENT", "助推创一流营商环境"),
    
    /**
     * 完成总商会/新联会交办的其他任务
     */
    KEY_WORK_OTHER_TASKS("KEY_WORK:OTHER_TASKS", "完成总商会/新联会交办的其他任务"),

    // ========== 一级枚举：公益慈善 ==========
    /**
     * 办好事、解难题、做公益情况（一级分类）
     */
    CHARITY("CHARITY", "办好事、解难题、做公益情况"),
    
    /**
     * 公益慈善贡献
     */
    CHARITY_CONTRIBUTION("CHARITY:CONTRIBUTION", "公益慈善贡献");

    @EnumValue
    private final String value;

    private final String label;

    AssessmentRuleTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static AssessmentRuleTypeEnum getEnumByValue(String value) {
        for (AssessmentRuleTypeEnum typeEnum : AssessmentRuleTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为一级枚举（不包含冒号的）
     *
     * @return true表示是一级枚举
     */
    public boolean isPrimaryType() {
        return !this.value.contains(":");
    }

    /**
     * 获取一级枚举类型
     *
     * @return 一级枚举类型
     */
    public AssessmentRuleTypeEnum getPrimaryType() {
        if (isPrimaryType()) {
            return this;
        }
        String primaryValue = this.value.split(":")[0];
        return getEnumByValue(primaryValue);
    }
}
