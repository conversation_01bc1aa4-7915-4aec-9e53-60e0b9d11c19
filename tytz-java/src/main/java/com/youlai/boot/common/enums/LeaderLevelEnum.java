package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 批示领导级别枚举
 */
@Getter
public enum LeaderLevelEnum implements IBaseEnum<String> {

    DISTRICT("DISTRICT", "区级"), // 区级
    MUNICIPAL("MUNICIPAL", "市级"), // 市级
    PROVINCIAL("PROVINCIAL", "省级"), // 省级
    NATIONAL("NATIONAL", "国家级"); // 国家级

    private final String value; // 编码（如用于数据库存储）
    private final String label; // 描述（中文名称）

    LeaderLevelEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 通过value获取枚举
     *
     * @param value
     * @return
     */
    public static LeaderLevelEnum getEnumByValue(String value) {
        for (LeaderLevelEnum leaderLevelEnum : LeaderLevelEnum.values()) {
            if (leaderLevelEnum.getValue().equals(value)) {
                return leaderLevelEnum;
            }
        }
        return null;
    }

    /**
     * 通过某个字符串获取对应的枚举
     * HACK: 临时性处理方法，营商环境问题反馈对应表中存的是Q（区级）和S（市级），这里主要是用来处理这个的
     * 
     * @param value
     * @return
     */
    public static LeaderLevelEnum getEnumByValueHack(String value) {
        if ("Q".equals(value)) {
            return LeaderLevelEnum.DISTRICT;
        } else if ("S".equals(value)) {
            return LeaderLevelEnum.MUNICIPAL;
        }
        return null;
    }
}