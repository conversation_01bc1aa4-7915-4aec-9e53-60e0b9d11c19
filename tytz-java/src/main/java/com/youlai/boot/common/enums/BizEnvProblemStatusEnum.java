package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 营商环境问题状态枚举
 * 
 */
@Getter
public enum BizEnvProblemStatusEnum implements IBaseEnum<String> {

    SUBMITTED("SUBMITTED", "已提交"),
    ADOPTED("ADOPTED", "已采纳"),
    REJECTED("REJECTED", "不予采纳"),
    INSTRUCTED_DISTRICT("INSTRUCTED_DISTRICT", "得到区领导批示"),
    INSTRUCTED_CITY("INSTRUCTED_CITY", "得到市领导批示");

    private final String value;

    private final String label;

    BizEnvProblemStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

}
