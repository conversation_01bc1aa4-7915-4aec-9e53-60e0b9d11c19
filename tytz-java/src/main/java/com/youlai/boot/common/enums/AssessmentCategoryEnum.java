package com.youlai.boot.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.youlai.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 考核规则类别枚举
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
public enum AssessmentCategoryEnum implements IBaseEnum<String> {

    /**
     * 总商会
     */
    ZSH("ZSH", "总商会"),

    /**
     * 新联会
     */
    XLH("XLH", "新联会");

    @EnumValue
    private final String value;

    private final String label;

    AssessmentCategoryEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static AssessmentCategoryEnum getEnumByValue(String value) {
        for (AssessmentCategoryEnum categoryEnum : AssessmentCategoryEnum.values()) {
            if (categoryEnum.getValue().equals(value)) {
                return categoryEnum;
            }
        }
        return null;
    }
}
