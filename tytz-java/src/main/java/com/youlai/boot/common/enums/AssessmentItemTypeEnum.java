package com.youlai.boot.common.enums;

import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 履职考核项目类型枚举
 *
 */
@Getter
public enum AssessmentItemTypeEnum implements IBaseEnum<String> {
    MEETING("MEETING", "参加会议"),
    ACTIVITY("ACTIVITY", "参加活动"),
    KEY_WORK("KEY_WORK", "年度重点工作"),
    ENVIRONMENT("ENVIRONMENT", "营商环境问题报送"),
    OPINION("OPINION", "意见建议"),
    CHARITY("CHARITY", "公益慈善");

    private final String value;

    private final String label;

    AssessmentItemTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 通过value获取枚举
     *
     * @param value
     * @return
     */
    public static AssessmentItemTypeEnum getEnumByValue(String value) {
        for (AssessmentItemTypeEnum typeEnum : AssessmentItemTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }
}
