package com.youlai.boot.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.system.model.entity.UserDept;

public interface UserDeptService extends IService<UserDept> {
    /**
     * 保存用户部门
     * 
     * @param userId  用户ID
     * @param deptIds 部门ID列表
     * @return true|false
     */
    boolean saveUserDepts(Long userId, List<Long> deptIds);
}
