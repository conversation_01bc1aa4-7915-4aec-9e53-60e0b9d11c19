package com.youlai.boot.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.system.mapper.UserDeptMapper;
import com.youlai.boot.system.model.entity.UserDept;
import com.youlai.boot.system.service.UserDeptService;

import cn.hutool.core.collection.CollectionUtil;

@Service
public class UserDeptServiceImpl extends ServiceImpl<UserDeptMapper, UserDept> implements UserDeptService {

    /**
     * 保存用户部门关联数据
     * 
     * @param userId  用户ID
     * @param deptIds 部门ID列表
     * @return true|false
     */
    @Override
    public boolean saveUserDepts(Long userId, List<Long> deptIds) {
        // 删除原先的用户部门关联数据
        this.baseMapper.deleteByUserIdsAndDeptIds(List.of(userId), deptIds);
        // 重新插入新的用户部门数据
        if (CollectionUtil.isNotEmpty(deptIds)) {
            List<UserDept> userDepts = deptIds.stream()
                    .map(deptId -> {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(userId);
                        userDept.setDeptId(deptId);
                        return userDept;
                    })
                    .collect(Collectors.toList());
            return this.saveBatch(userDepts);
        }
        return false;
    }
}
