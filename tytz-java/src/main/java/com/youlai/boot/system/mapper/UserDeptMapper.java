package com.youlai.boot.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youlai.boot.system.model.entity.UserDept;

/**
 * 用户部门关联表访问层
 */
@Mapper
public interface UserDeptMapper extends BaseMapper<UserDept> {

    /**
     * 批量物理删除用户部门关联数据
     * 
     * @param deptIds 部门ID列表
     * 
     */
    int deleteByDeptIds(List<Long> deptIds);

    /**
     * 批量物理删除用户部门关联数据
     * 
     *
     * @param userIds 用户ID列表
     */
    int deleteByUserIds(List<Long> userIds);

    /**
     * 通过条件物理删除用户部门关联数据
     * 
     * @param userIds 用户ID列表
     * @param deptIds 部门ID列表
     */
    int deleteByUserIdsAndDeptIds(List<Long> userIds, List<Long> deptIds);

}
