package com.youlai.boot.system.enums;

import com.youlai.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 系统部门类型枚举
 */
@Getter
public enum DeptTypeEnum implements IBaseEnum<String> {
    DEPT("DEPT", "部门"),
    POSITION("POSITION", "职务");

    private final String value;

    private final String label;

    DeptTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

}
