package com.youlai.boot.modules.assessment.model.bo;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;

import lombok.Data;

/**
 * 履职考核缓存对象
 */
@Data
public class AssessmentRuleCacheBO {

    /**
     * id
     */
    private Long id;

    /**
     * 履职项目类别（ZSH: 总商会, XLH: 新联会）
     */
    private AssessmentCategoryEnum category;

    /**
     * 履职项目名称
     */
    private String ruleName;

    /**
     * 履职项目编号（ACTIVITY: 活动, MEETING: 会议, KEY_WORK: 重点工作, ENVIRONMENT: 营商环境问题报送,
     * OPINION: 意见建议, CHARITY: 公益慈善活动...）
     */
    private String ruleCode;

    /**
     * 父级规则ID
     */
    private Long parentId;

    /**
     * 父级规则路径
     */
    private String treePath;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 得分（非空，默认值为0，主分类为0，规则才有具体的分数）
     */
    private Integer score;

    /**
     * 得分上限（为null时无限制）
     */
    private Integer limitMax;

    /**
     * 得分下限（为null时无限制）
     */
    private Integer limitMin;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
}
