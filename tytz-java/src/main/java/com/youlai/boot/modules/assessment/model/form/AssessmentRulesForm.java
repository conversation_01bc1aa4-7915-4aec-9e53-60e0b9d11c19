package com.youlai.boot.modules.assessment.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;

/**
 * 履职考核规则表单对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Schema(description = "履职考核规则表单对象")
public class AssessmentRulesForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "规则ID")
    private Long id;

    @Schema(description = "履职项目类别")
    @NotNull(message = "履职项目类别不能为空")
    private AssessmentCategoryEnum category;

    @Schema(description = "履职项目名称")
    @NotBlank(message = "履职项目名称不能为空")
    @Size(max = 255, message = "履职项目名称长度不能超过255个字符")
    private String ruleName;

    @Schema(description = "履职项目编号")
    @NotBlank(message = "履职项目编号不能为空")
    @Size(max = 64, message = "履职项目编号长度不能超过64个字符")
    private String ruleCode;

    @Schema(description = "父级规则ID")
    @NotNull(message = "父级规则ID不能为空")
    private Long parentId;

    @Schema(description = "父级规则路径")
    @Size(max = 255, message = "父级规则路径长度不能超过255个字符")
    private String treePath;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "得分")
    @NotNull(message = "得分不能为空")
    private Integer score;

    @Schema(description = "得分上限")
    private Integer limitMax;

    @Schema(description = "得分下限")
    private Integer limitMin;
}
