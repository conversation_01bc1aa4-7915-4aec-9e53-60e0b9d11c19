package com.youlai.boot.modules.assessment.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.assessment.model.bo.AssessmentRuleCacheBO;
import com.youlai.boot.modules.assessment.model.dto.AssessmentRulesPageDTO;
import com.youlai.boot.modules.assessment.model.entity.AssessmentRules;
import com.youlai.boot.modules.assessment.model.form.AssessmentRulesForm;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesPageVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 履职考核规则对象转换器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper(componentModel = "spring")
public interface AssessmentRulesConverter {

    /**
     * 实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    @Mapping(target = "categoryLabel", expression = "java(entity.getCategory() != null ? entity.getCategory().getLabel() : null)")
    @Mapping(target = "children", ignore = true)
    AssessmentRulesVO toVO(AssessmentRules entity);

    /**
     * 分页实体转换为分页VO
     *
     * @param page 分页实体对象
     * @return 分页VO对象
     */
    Page<AssessmentRulesVO> toPageVO(Page<AssessmentRules> page);

    /**
     * 表单转换为实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    AssessmentRules toEntity(AssessmentRulesForm form);

    /**
     * 实体转换为表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    @InheritInverseConfiguration(name = "toEntity")
    AssessmentRulesForm toForm(AssessmentRules entity);

    /**
     * DTO 转 VO
     * 
     * @param dto DTO对象
     * @return VO对象
     */
    @Mapping(target = "categoryLabel", expression = "java(dto.getCategory() != null ? dto.getCategory().getLabel() : null)")
    AssessmentRulesPageVO toPageVO(AssessmentRulesPageDTO dto);

    /**
     * entity 转 CacheBO
     * 
     * 
     * @param entity entity对象
     * @return BO对象
     */
    AssessmentRuleCacheBO toCacheBO(AssessmentRules entity);
}
