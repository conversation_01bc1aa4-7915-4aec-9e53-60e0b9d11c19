package com.youlai.boot.modules.assessment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScore;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentRuleTypeEnum;

/**
 * 履职考核得分总表服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface AssessmentScoreService extends IService<AssessmentScore> {

    /**
     * 履职考核得分总表分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<AssessmentScoreVO> getAssessmentScorePage(AssessmentScoreQuery queryParams);

    /**
     * 获取履职考核得分总表表单数据
     *
     * @param id 得分ID
     * @return 表单数据
     */
    AssessmentScoreForm getAssessmentScoreFormData(Long id);

    /**
     * 新增履职考核得分总表
     *
     * @param formData 得分表单对象
     * @return 是否成功
     */
    boolean saveAssessmentScore(AssessmentScoreForm formData);

    /**
     * 修改履职考核得分总表
     *
     * @param id       得分ID
     * @param formData 得分表单对象
     * @return 是否成功
     */
    boolean updateAssessmentScore(Long id, AssessmentScoreForm formData);

    /**
     * 删除履职考核得分总表
     *
     * @param ids 得分ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteAssessmentScore(String ids);

    /**
     * 更新用户的某个类型得分
     *
     * @param category   得分归类
     * @param memberId   用户ID
     * @param year       年度
     * @param type       得分类型
     * @param scoreDelta 得分变化量（可为负数）
     * @return 是否成功
     */
    boolean updateMemberScore(AssessmentCategoryEnum category, Long memberId, Integer year,
            AssessmentRuleTypeEnum type, Integer scoreDelta);

    /**
     * 重新计算用户的总得分
     *
     * @param category 得分归类
     * @param memberId 用户ID
     * @param year     年度
     * @return 是否成功
     */
    boolean recalculateTotalScore(AssessmentCategoryEnum category, Long memberId, Integer year);
}
