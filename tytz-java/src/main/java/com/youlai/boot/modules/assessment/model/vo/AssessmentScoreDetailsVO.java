package com.youlai.boot.modules.assessment.model.vo;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentRuleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 履职考核得分细则表视图对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Schema(description = "履职考核得分细则表视图对象")
public class AssessmentScoreDetailsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "得分细则ID")
    private Long id;

    @Schema(description = "得分归类")
    private AssessmentCategoryEnum category;

    @Schema(description = "得分归类标签")
    private String categoryLabel;

    @Schema(description = "得分类型")
    private AssessmentRuleTypeEnum type;

    @Schema(description = "得分类型标签")
    private String typeLabel;

    @Schema(description = "被考核人用户ID")
    private Long memberId;

    @Schema(description = "会员名称")
    private String memberName;

    @Schema(description = "对应的考核规则ID")
    private Long ruleId;

    @Schema(description = "考核规则名称")
    private String ruleName;

    @Schema(description = "规则得分")
    private Integer ruleScore;

    @Schema(description = "得分对应的数据ID")
    private Long dataId;

    @Schema(description = "数据标题/名称")
    private String dataTitle;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
