package com.youlai.boot.modules.assessment.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentRuleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核得分细则表查询对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Schema(description = "履职考核得分细则表查询对象")
@Getter
@Setter
public class AssessmentScoreDetailsQuery extends BasePageQuery {

    @Schema(description = "得分归类")
    private AssessmentCategoryEnum category;

    @Schema(description = "得分类型")
    private AssessmentRuleTypeEnum type;

    @Schema(description = "被考核人用户ID")
    private Long memberId;

    @Schema(description = "会员名称")
    private String memberName;

    @Schema(description = "对应的考核规则ID")
    private Long ruleId;

    @Schema(description = "得分对应的数据ID")
    private Long dataId;

    @Schema(description = "年度")
    private Integer year;
}
