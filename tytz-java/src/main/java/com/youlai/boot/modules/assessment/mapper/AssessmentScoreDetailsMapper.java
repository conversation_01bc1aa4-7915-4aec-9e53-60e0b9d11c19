package com.youlai.boot.modules.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScoreDetails;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailDataQuery;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailsQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 履职考核得分细则表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper
public interface AssessmentScoreDetailsMapper extends BaseMapper<AssessmentScoreDetails> {

    /**
     * 获取履职考核得分细则表分页数据
     *
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 考核得分细则分页数据
     */
    Page<AssessmentScoreDetailsVO> getAssessmentScoreDetailsPage(Page<AssessmentScoreDetailsVO> page,
            AssessmentScoreDetailsQuery queryParams);

    /**
     * 获取用户的得分细则详情
     *
     * @param memberId 用户ID
     * @param year     年度
     * @return 得分细则详情列表
     */
    List<AssessmentScoreDetailsVO> getMemberScoreDetails(Long memberId, Integer year);

    /**
     * 分页获取参加活动得分对应的数据列表
     * 
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<AssessmentScoreDetailDataVO> getAssessmentScoreDetailActivityData(Page<AssessmentScoreDetailDataVO> page,
            AssessmentScoreDetailDataQuery queryParams);

    /**
     * 分页获取参加会议得分对应的数据列表
     * 
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<AssessmentScoreDetailDataVO> getAssessmentScoreDetailMeetingData(Page<AssessmentScoreDetailDataVO> page,
            AssessmentScoreDetailDataQuery queryParams);

    /**
     * 分页获取重点工作得分对应的数据列表
     * 
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<AssessmentScoreDetailDataVO> getAssessmentScoreDetailKeyWorkData(Page<AssessmentScoreDetailDataVO> page,
            AssessmentScoreDetailDataQuery queryParams);

    /**
     * 分页获取营商环境问题得分对应的数据列表
     * 
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<AssessmentScoreDetailDataVO> getAssessmentScoreDetailEnvironmentData(Page<AssessmentScoreDetailDataVO> page,
            AssessmentScoreDetailDataQuery queryParams);

    /**
     * 分页获取意见建议得分对应的数据列表
     * 
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<AssessmentScoreDetailDataVO> getAssessmentScoreDetailOpinionData(Page<AssessmentScoreDetailDataVO> page,
            AssessmentScoreDetailDataQuery queryParams);

    /**
     * 分页获取公益活动得分对应的数据列表
     * 
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 分页数据
     */
    Page<AssessmentScoreDetailDataVO> getAssessmentScoreDetailCharityData(Page<AssessmentScoreDetailDataVO> page,
            AssessmentScoreDetailDataQuery queryParams);
}
