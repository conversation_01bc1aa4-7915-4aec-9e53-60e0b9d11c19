package com.youlai.boot.modules.assessment.model.dto;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 履职考核规则分页DTO
 *
 */
@Data
public class AssessmentRulesPageDTO {
    @Schema(description = "规则ID")
    private Long id;

    @Schema(description = "履职项目类别")
    private AssessmentCategoryEnum category;

    @Schema(description = "履职项目名称")
    private String itemName;

    @Schema(description = "履职项目编号")
    private String itemCode;

    @Schema(description = "履职项目合并行数")
    private Integer itemSpan;

    @Schema(description = "履职规则名称")
    private String ruleName;

    @Schema(description = "履职规则编号")
    private String ruleCode;

    @Schema(description = "履职规则得分")
    private Integer score;

    @Schema(description = "履职规则得分上限")
    private Integer limitMax;

    @Schema(description = "履职规则得分下限")
    private Integer limitMin;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "父级规则ID")
    private Long parentId;

    @Schema(description = "父级规则路径")
    private String treePath;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;
}
