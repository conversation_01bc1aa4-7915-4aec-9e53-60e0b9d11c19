package com.youlai.boot.modules.assessment.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核规则查询对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Schema(description = "履职考核规则查询对象")
@Getter
@Setter
public class AssessmentRulesQuery extends BasePageQuery {

    @Schema(description = "履职项目类别")
    @NotNull(message = "履职项目所属部门不能为空")
    private AssessmentCategoryEnum category;

    @Schema(description = "履职项目名称")
    private String ruleName;

    @Schema(description = "履职项目编号")
    private String ruleCode;

    @Schema(description = "父级规则ID")
    private Long parentId;
}
