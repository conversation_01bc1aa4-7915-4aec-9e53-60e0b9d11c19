package com.youlai.boot.modules.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScore;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 履职考核得分总表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper
public interface AssessmentScoreMapper extends BaseMapper<AssessmentScore> {

    /**
     * 获取履职考核得分总表分页数据
     *
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 考核得分分页数据
     */
    Page<AssessmentScoreVO> getAssessmentScorePage(Page<AssessmentScoreVO> page, AssessmentScoreQuery queryParams);
}
