package com.youlai.boot.modules.assessment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核得分细则表实体对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@TableName("tsz_assessment_score_details")
public class AssessmentScoreDetails extends BaseEntity {

    /**
     * 得分归类(ZSH: 总商会 XLH: 新联会)
     */
    private AssessmentCategoryEnum category;

    /**
     * 得分类型（MEETING: 会议, ACTIVITY: 活动, KEY_WORK: 重点工作, ENVIRONMENT: 营商环境问题报送,
     * OPINION: 意见建议, CHARITY: 公益慈善活动...）
     */
    private AssessmentItemTypeEnum type;

    /**
     * 被考核人（总商会/新联会会员）用户id
     */
    private Long memberId;

    /**
     * 对应的考核规则id
     */
    private Long ruleId;

    /**
     * 得分对应的数据id（会议id、活动id、重点工作id等）
     */
    private Long dataId;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 备注
     */
    private String remark;
}
