package com.youlai.boot.modules.assessment.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScoreDetails;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreDetailsForm;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 履职考核得分细则表对象转换器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper(componentModel = "spring")
public interface AssessmentScoreDetailsConverter {

    /**
     * 实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    @Mapping(target = "categoryLabel", expression = "java(entity.getCategory() != null ? entity.getCategory().getLabel() : null)")
    @Mapping(target = "typeLabel", expression = "java(entity.getType() != null ? entity.getType().getLabel() : null)")
    @Mapping(target = "memberName", ignore = true)
    @Mapping(target = "ruleName", ignore = true)
    @Mapping(target = "ruleScore", ignore = true)
    @Mapping(target = "dataTitle", ignore = true)
    AssessmentScoreDetailsVO toVO(AssessmentScoreDetails entity);

    /**
     * 分页实体转换为分页VO
     *
     * @param page 分页实体对象
     * @return 分页VO对象
     */
    @Mapping(target = "convert", ignore = true)
    @Mapping(target = "countId", ignore = true)
    @Mapping(target = "maxLimit", ignore = true)
    @Mapping(target = "optimizeCountSql", ignore = true)
    @Mapping(target = "optimizeJoinOfCountSql", ignore = true)
    @Mapping(target = "orders", ignore = true)
    @Mapping(target = "searchCount", ignore = true)
    Page<AssessmentScoreDetailsVO> toPageVO(Page<AssessmentScoreDetails> page);

    /**
     * 表单转换为实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    AssessmentScoreDetails toEntity(AssessmentScoreDetailsForm form);

    /**
     * 实体转换为表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    @InheritInverseConfiguration(name = "toEntity")
    AssessmentScoreDetailsForm toForm(AssessmentScoreDetails entity);
}
