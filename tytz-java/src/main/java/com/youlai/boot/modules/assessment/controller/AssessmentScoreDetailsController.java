package com.youlai.boot.modules.assessment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreDetailsForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailDataQuery;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailsQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO;
import com.youlai.boot.modules.assessment.service.AssessmentScoreDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 履职考核得分细则表前端控制层
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Tag(name = "履职考核得分细则表接口")
@RestController
@RequestMapping("/api/v1/assessment-score-details")
@RequiredArgsConstructor
public class AssessmentScoreDetailsController {

    private final AssessmentScoreDetailsService assessmentScoreDetailsService;

    @Operation(summary = "履职考核得分细则表分页列表")
    @GetMapping("/page")
    public PageResult<AssessmentScoreDetailsVO> getAssessmentScoreDetailsPage(AssessmentScoreDetailsQuery queryParams) {
        IPage<AssessmentScoreDetailsVO> result = assessmentScoreDetailsService
                .getAssessmentScoreDetailsPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "获取用户的得分细则详情")
    @GetMapping("/member/{memberId}")
    public Result<List<AssessmentScoreDetailsVO>> getMemberScoreDetails(
            @Parameter(description = "用户ID") @PathVariable Long memberId,
            @Parameter(description = "年度") @RequestParam Integer year) {
        List<AssessmentScoreDetailsVO> result = assessmentScoreDetailsService.getMemberScoreDetails(memberId, year);
        return Result.success(result);
    }

    @Operation(summary = "新增履职考核得分细则表")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('assessment:score_details:add')")
    public Result<Void> saveAssessmentScoreDetails(@RequestBody @Valid AssessmentScoreDetailsForm formData) {
        boolean result = assessmentScoreDetailsService.saveAssessmentScoreDetails(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取履职考核得分细则表表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('assessment:score_details:get')")
    public Result<AssessmentScoreDetailsForm> getAssessmentScoreDetailsForm(
            @Parameter(description = "得分细则ID") @PathVariable Long id) {
        AssessmentScoreDetailsForm formData = assessmentScoreDetailsService.getAssessmentScoreDetailsFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改履职考核得分细则表")
    @PutMapping(value = "/{id}")
    @PreAuthorize("@ss.hasPerm('assessment:score_details:edit')")
    public Result<Void> updateAssessmentScoreDetails(
            @Parameter(description = "得分细则ID") @PathVariable Long id,
            @RequestBody @Validated AssessmentScoreDetailsForm formData) {
        boolean result = assessmentScoreDetailsService.updateAssessmentScoreDetails(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除履职考核得分细则表")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('assessment:score_details:delete')")
    public Result<Void> deleteAssessmentScoreDetails(
            @Parameter(description = "得分细则ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        boolean result = assessmentScoreDetailsService.deleteAssessmentScoreDetails(ids);
        return Result.judge(result);
    }

    @Operation(summary = "分页获取履职考核具体项目的数据列表预计对应得分")
    @GetMapping("/detail-data/page")
    public PageResult<AssessmentScoreDetailDataVO> AssessmentScoreDetailDataQuery(
            @Valid AssessmentScoreDetailDataQuery queryParams) {
        return PageResult.success(assessmentScoreDetailsService.getAssessmentScoreDetailDataPage(queryParams));
    }
}
