package com.youlai.boot.modules.assessment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.common.enums.AssessmentItemTypeEnum;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScoreDetails;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreDetailsForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailDataQuery;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailsQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO;

import java.util.List;

/**
 * 履职考核得分细则表服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface AssessmentScoreDetailsService extends IService<AssessmentScoreDetails> {

    /**
     * 履职考核得分细则表分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<AssessmentScoreDetailsVO> getAssessmentScoreDetailsPage(AssessmentScoreDetailsQuery queryParams);

    /**
     * 获取用户的得分细则详情
     *
     * @param memberId 用户ID
     * @param year     年度
     * @return 得分细则详情列表
     */
    List<AssessmentScoreDetailsVO> getMemberScoreDetails(Long memberId, Integer year);

    /**
     * 获取履职考核得分细则表表单数据
     *
     * @param id 得分细则ID
     * @return 表单数据
     */
    AssessmentScoreDetailsForm getAssessmentScoreDetailsFormData(Long id);

    /**
     * 新增履职考核得分细则表
     *
     * @param formData 得分细则表单对象
     * @return 是否成功
     */
    boolean saveAssessmentScoreDetails(AssessmentScoreDetailsForm formData);

    /**
     * 修改履职考核得分细则表
     *
     * @param id       得分细则ID
     * @param formData 得分细则表单对象
     * @return 是否成功
     */
    boolean updateAssessmentScoreDetails(Long id, AssessmentScoreDetailsForm formData);

    /**
     * 删除履职考核得分细则表
     *
     * @param ids 得分细则ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteAssessmentScoreDetails(String ids);

    /**
     * 通过dataId列表和得分类型删除履职得分细则数据
     * 
     * @param dataIds 数据id列表
     * @param type    得分类型
     * @return 是否成功
     */
    boolean deleteByDataIds(List<Long> dataIds, AssessmentItemTypeEnum type);

    /**
     * 获取履职考核项目对应的数据以及得分列表
     * 
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<AssessmentScoreDetailDataVO> getAssessmentScoreDetailDataPage(AssessmentScoreDetailDataQuery queryParams);

    /**
     * 重新统计并同步所有用户的履职考核得分
     * 
     * @return 是否成功
     */
    boolean reCalculateAllScore();
}
