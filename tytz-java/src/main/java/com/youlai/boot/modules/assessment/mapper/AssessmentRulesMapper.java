package com.youlai.boot.modules.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.assessment.model.dto.AssessmentRulesPageDTO;
import com.youlai.boot.modules.assessment.model.entity.AssessmentRules;
import com.youlai.boot.modules.assessment.model.query.AssessmentRulesQuery;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 履职考核规则Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper
public interface AssessmentRulesMapper extends BaseMapper<AssessmentRules> {

    /**
     * 获取履职考核规则分页数据
     *
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 考核规则分页数据
     */
    Page<AssessmentRulesPageDTO> getAssessmentRulesPage(Page<AssessmentRulesPageDTO> page,
            AssessmentRulesQuery queryParams);

    /**
     * 获取履职考核规则列表数据
     *
     * @param queryParams 查询参数
     * @return 考核规则列表数据
     */
    List<AssessmentRulesPageDTO> getAssessmentRulesPage(@Param("queryParams") AssessmentRulesQuery queryParams);
}
