package com.youlai.boot.modules.assessment.model.query;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentItemTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核得分对应数据列表及得分查询对象
 */
@Schema(description = "履职考核得分对应数据列表及得分查询对象")
@Getter
@Setter
public class AssessmentScoreDetailDataQuery extends BasePageQuery {

    @Schema(description = "得分归类")
    @NotNull(message = "得分归类不能为空")
    private AssessmentCategoryEnum category;

    @Schema(description = "被考核人用户ID")
    @NotNull(message = "被考核人用户ID不能为空")
    private Long memberId;

    @Schema(description = "数据类别")
    @NotNull(message = "数据类别不能为空")
    private AssessmentItemTypeEnum dataType;

    @Schema(description = "年度")
    @NotNull(message = "年度不能为空")
    private Integer year;
}
