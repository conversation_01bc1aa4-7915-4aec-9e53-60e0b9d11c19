package com.youlai.boot.modules.assessment.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.modules.assessment.constant.AssessmentConstants;
import com.youlai.boot.modules.assessment.converter.AssessmentRulesConverter;
import com.youlai.boot.modules.assessment.mapper.AssessmentRulesMapper;
import com.youlai.boot.modules.assessment.model.bo.AssessmentRuleCacheBO;
import com.youlai.boot.modules.assessment.model.entity.AssessmentRules;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 履职考核计分规则缓存工具类
 *
 * @description 提供履职规则缓存的统一管理，避免service层循环依赖
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AssessmentRulesCacheUtils {

    private final RedisTemplate<String, Object> redisTemplate;
    private final AssessmentRulesMapper assessmentRulesMapper;
    private final AssessmentRulesConverter assessmentRulesConverter;

    /**
     * 初始化履职规则缓存
     *
     * @description 系统启动时调用，初始化所有履职规则缓存
     */
    public void initAssessmentRulesCache() {
        log.info("初始化履职规则缓存");
        refreshAllAssessmentRulesCache();
    }

    /**
     * 刷新所有履职规则缓存
     *
     * @description 清除所有缓存并重新加载数据库中的所有履职规则
     */
    public void refreshAllAssessmentRulesCache() {
        // 清除履职规则缓存
        redisTemplate.delete(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX);

        // 获取所有有效的履职规则
        List<AssessmentRules> rules = assessmentRulesMapper.selectList(
                new LambdaQueryWrapper<AssessmentRules>().eq(AssessmentRules::getIsDeleted, 0));
        if (rules.isEmpty()) {
            log.warn("履职规则缓存刷新失败，数据库中不存在有效的履职规则");
            return;
        }

        // 转换为缓存对象并存储
        Map<String, AssessmentRuleCacheBO> cacheMap = new HashMap<>();
        // 遍历查询到的列表并转成map
        rules.forEach(rule -> {
            AssessmentRuleCacheBO ruleCacheBO = assessmentRulesConverter.toCacheBO(rule);
            cacheMap.put(rule.getId().toString(), ruleCacheBO);
            cacheMap.put(rule.getCategory().getValue() + ":" + rule.getRuleCode(),
                    ruleCacheBO);
        });

        // 转换为缓存对象并存储
        redisTemplate.opsForHash().putAll(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheMap);
        // rules.stream()
        // .collect(Collectors.toMap(
        // rule -> rule.getCategory().getValue() + ":" + rule.getRuleCode(),
        // rule -> assessmentRulesConverter.toCacheBO(rule))));

        log.info("履职规则缓存刷新完成，共缓存 {} 条规则", cacheMap.size());
    }

    /**
     * 刷新单条履职规则缓存
     *
     * @param category 履职项目类别
     * @param ruleCode 履职规则编号
     * @description 根据类别和规则编号刷新指定的履职规则缓存
     */
    public void refreshSingleAssessmentRuleCache(AssessmentCategoryEnum category, String ruleCode) {
        String cacheItemKey = category.getValue() + ":" + ruleCode;

        // 清除指定的履职规则缓存
        redisTemplate.opsForHash().delete(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey);

        // 从数据库获取最新数据
        AssessmentRules rule = assessmentRulesMapper.selectOne(
                new LambdaQueryWrapper<AssessmentRules>()
                        .eq(AssessmentRules::getCategory, category)
                        .eq(AssessmentRules::getRuleCode, ruleCode)
                        .eq(AssessmentRules::getIsDeleted, 0));

        if (rule != null) {
            // 转换为缓存对象并更新缓存
            AssessmentRuleCacheBO cacheBO = assessmentRulesConverter.toCacheBO(rule);
            redisTemplate.opsForHash().put(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey,
                    cacheBO);
            redisTemplate.opsForHash().put(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX,
                    rule.getId().toString(),
                    cacheBO);
            log.debug("履职规则缓存已更新: {}", cacheItemKey);
        } else {
            log.warn("履职规则不存在，无法更新缓存: {}", cacheItemKey);
        }
    }

    /**
     * 获取单条履职规则缓存
     *
     * @param category 履职项目类别
     * @param ruleCode 履职规则编号
     * @return 履职规则缓存对象，如果不存在则返回null
     * @description 从缓存中获取履职规则，如果缓存中不存在则从数据库加载并更新缓存
     */
    public AssessmentRuleCacheBO getAssessmentRuleCache(AssessmentCategoryEnum category, String ruleCode) {
        String cacheItemKey = category.getValue() + ":" + ruleCode;

        // 先从缓存中获取
        AssessmentRuleCacheBO cacheBO = (AssessmentRuleCacheBO) redisTemplate.opsForHash()
                .get(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey);

        if (cacheBO == null) {
            // 缓存中不存在，从数据库加载
            AssessmentRules rule = assessmentRulesMapper.selectOne(
                    new LambdaQueryWrapper<AssessmentRules>()
                            .eq(AssessmentRules::getCategory, category)
                            .eq(AssessmentRules::getRuleCode, ruleCode)
                            .eq(AssessmentRules::getIsDeleted, 0));

            if (rule != null) {
                // 转换为缓存对象并更新缓存
                cacheBO = assessmentRulesConverter.toCacheBO(rule);
                redisTemplate.opsForHash().put(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey,
                        cacheBO);
                redisTemplate.opsForHash().put(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX,
                        rule.getId().toString(),
                        cacheBO);
                log.debug("履职规则缓存已更新: {}", cacheItemKey);
            }
        }

        return cacheBO;
    }

    /**
     * 通过ID获取单条缓存的考核规则缓存对象
     *
     * @param ruleId 规则ID
     * @return 履职规则缓存对象，如果不存在则返回null
     */
    public AssessmentRuleCacheBO getAssessmentRuleCache(Long ruleId) {
        String cacheItemKey = ruleId.toString();

        // 先从缓存中获取
        AssessmentRuleCacheBO cacheBO = (AssessmentRuleCacheBO) redisTemplate.opsForHash()
                .get(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey);

        if (cacheBO == null) {
            // 缓存中不存在，从数据库加载
            AssessmentRules rule = assessmentRulesMapper.selectById(ruleId);

            if (rule != null) {
                // 转换为缓存对象并更新缓存
                cacheBO = assessmentRulesConverter.toCacheBO(rule);
                redisTemplate.opsForHash().put(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey,
                        cacheBO);
                redisTemplate.opsForHash().put(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX,
                        rule.getCategory().getValue() + ":" + rule.getRuleCode(),
                        cacheBO);
                log.debug("履职规则缓存已更新: {}", cacheItemKey);
            }
        }

        return cacheBO;
    }

    /**
     * 删除单条履职规则缓存
     *
     * @param category 履职项目类别
     * @param ruleCode 履职规则编号
     * @description 从缓存中删除指定的履职规则
     */
    public void removeAssessmentRuleCache(AssessmentCategoryEnum category, String ruleCode) {
        String cacheItemKey = category.getValue() + ":" + ruleCode;
        // 从缓存中删除
        AssessmentRuleCacheBO cacheBO = (AssessmentRuleCacheBO) redisTemplate.opsForHash()
                .get(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey);
        if (cacheBO == null) {
            log.debug("履职规则缓存不存在，无法删除: {}", cacheItemKey);
            return;
        }
        // 删除id对应的那条缓存
        redisTemplate.opsForHash().delete(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX,
                cacheBO.getId().toString());
        // 删除对应的缓存
        redisTemplate.opsForHash().delete(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX, cacheItemKey);
        log.debug("履职规则缓存已删除: {}", cacheItemKey);
    }

    /**
     * 清除所有履职规则缓存
     *
     * @description 清除所有履职规则缓存，通常在系统维护时使用
     */
    public void clearAllAssessmentRulesCache() {
        redisTemplate.delete(AssessmentConstants.ASSESSMENT_RULES_MAP_CACHE_PREFIX);
        log.info("所有履职规则缓存已清除");
    }
}
