package com.youlai.boot.modules.assessment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO;
import com.youlai.boot.modules.assessment.service.AssessmentScoreDetailsService;
import com.youlai.boot.modules.assessment.service.AssessmentScoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 履职考核得分总表前端控制层
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Tag(name = "履职考核得分总表接口")
@RestController
@RequestMapping("/api/v1/assessment-score")
@RequiredArgsConstructor
public class AssessmentScoreController {

    private final AssessmentScoreService assessmentScoreService;
    private final AssessmentScoreDetailsService assessmentScoreDetailsService;

    @Operation(summary = "履职考核得分总表分页列表")
    @GetMapping("/page")
    public PageResult<AssessmentScoreVO> getAssessmentScorePage(AssessmentScoreQuery queryParams) {
        IPage<AssessmentScoreVO> result = assessmentScoreService.getAssessmentScorePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增履职考核得分总表")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('assessment:score:add')")
    public Result<Void> saveAssessmentScore(@RequestBody @Valid AssessmentScoreForm formData) {
        boolean result = assessmentScoreService.saveAssessmentScore(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取履职考核得分总表表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('assessment:score:get')")
    public Result<AssessmentScoreForm> getAssessmentScoreForm(
            @Parameter(description = "得分ID") @PathVariable Long id) {
        AssessmentScoreForm formData = assessmentScoreService.getAssessmentScoreFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改履职考核得分总表")
    @PutMapping(value = "/{id}")
    @PreAuthorize("@ss.hasPerm('assessment:score:edit')")
    public Result<Void> updateAssessmentScore(
            @Parameter(description = "得分ID") @PathVariable Long id,
            @RequestBody @Validated AssessmentScoreForm formData) {
        boolean result = assessmentScoreService.updateAssessmentScore(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除履职考核得分总表")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('assessment:score:delete')")
    public Result<Void> deleteAssessmentScore(
            @Parameter(description = "得分ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        boolean result = assessmentScoreService.deleteAssessmentScore(ids);
        return Result.judge(result);
    }

    @Operation(summary = "重新统计所有用户的履职考核得分")
    @GetMapping("/refresh")
    @PreAuthorize("@ss.hasPerm('assessment:score:refresh')")
    public Result<Void> reCalculateAllScore() {
        boolean result = assessmentScoreDetailsService.reCalculateAllScore();
        return Result.judge(result);
    }
}
