package com.youlai.boot.modules.assessment.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
// import java.time.LocalDateTime;
// import java.util.List;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;

/**
 * 履职考核规则视图对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Schema(description = "履职考核规则视图对象")
public class AssessmentRulesPageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "规则ID")
    private Long id;

    @Schema(description = "履职项目类别")
    private AssessmentCategoryEnum category;

    @Schema(description = "履职项目类别标签")
    private String categoryLabel;

    @Schema(description = "履职项目名称")
    private String itemName;

    @Schema(description = "履职项目编号")
    private String itemCode;

    @Schema(description = "履职项目合并行数")
    private Integer itemSpan;

    @Schema(description = "履职规则名称")
    private String ruleName;

    @Schema(description = "履职规则编号")
    private String ruleCode;

    // @Schema(description = "父级规则ID")
    // private Long parentId;

    // @Schema(description = "父级规则路径")
    // private String treePath;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "得分")
    private Integer score;

    @Schema(description = "得分上限")
    private Integer limitMax;

    @Schema(description = "得分下限")
    private Integer limitMin;

    // @Schema(description = "创建时间")
    // private LocalDateTime createTime;

    // @Schema(description = "更新时间")
    // private LocalDateTime updateTime;

}
