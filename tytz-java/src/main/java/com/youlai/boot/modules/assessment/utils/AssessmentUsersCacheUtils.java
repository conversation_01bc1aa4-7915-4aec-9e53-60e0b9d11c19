package com.youlai.boot.modules.assessment.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youlai.boot.modules.assessment.constant.AssessmentConstants;
import com.youlai.boot.system.mapper.DeptMapper;
import com.youlai.boot.system.mapper.UserDeptMapper;
import com.youlai.boot.system.model.entity.Dept;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 需要考核的用户id缓存
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AssessmentUsersCacheUtils {
    private final RedisTemplate<String, Object> redisTemplate;
    private final DeptMapper deptMapper;
    private final UserDeptMapper userDeptMapper;

    /**
     * 初始化需要考核的用户id缓存
     */
    public void initAssessmentUserIdsCache() {
        log.info("初始化需要考核的用户id缓存");
        refreshAssessmentUserIdsCache();
    }

    /**
     * 从数据库表中获取对应的map数据
     */
    private Map<String, List<Long>> getDeptCodeIdMap() {
        // 获取需要考核的用户id, 专为Map形式，key为code，value为id列表
        Map<String, List<Long>> userIdsMap = new HashMap<>();
        // 获取需要考核的部门map
        Map<String, Long> deptCodeIdMap = new HashMap<>();
        List<Dept> depts = deptMapper
                .selectList(new LambdaQueryWrapper<Dept>().eq(Dept::getIsDeleted, 0).in(Dept::getCode,
                        AssessmentConstants.ASSESSMENT_DEPTS_CODE_LIST))
                .stream().map(dept -> {
                    deptCodeIdMap.put(dept.getCode(), deptCodeIdMap.getOrDefault(dept.getCode(), new ArrayList<>())
                            .stream().distinct().toList());
                    return dept;
                }).toList();
        List<Long> deptIds = depts.stream().map(Dept::getId).toList();
        // 获取考核部门的子用户id列表
        deptIds.forEach(deptId -> {
            deptMapper
                    .selectList(new LambdaQueryWrapper<Dept>().eq(Dept::getIsDeleted, 0)
                            .apply("CONCAT(',', tree_path, ',', id, ',') LIKE CONCAT('%,', {0}, ',%')", deptId))
                    .stream().forEach(dept -> {
                        deptCodeIdMap.put(dept.getCode(), deptCodeIdMap.getOrDefault(dept.getCode(), new ArrayList<>())
                                .stream().distinct().toList());
                    });
        });
        return deptCodeIdMap;
    }

    /**
     * 刷新需要考核的用户id缓存
     */
    public void refreshAssessmentUserIdsCache() {
        // 清除缓存
        redisTemplate.delete(AssessmentConstants.ASSESSMENT_DEPTS_CACHE_KEY);
        Map<String, List<Long>> cacheMap = getDeptCodeIdMap();
        // 存入缓存
        redisTemplate.opsForValue().set(AssessmentConstants.ASSESSMENT_DEPTS_CACHE_KEY, cacheMap, 1800,
                TimeUnit.SECONDS);
        log.info("刷新需要考核的用户id缓存");
    }

    /**
     * 获取需要考核的用户id列表
     */
    public List<Long> getAssessmentUserIds() {
        if (redisTemplate.hasKey(AssessmentConstants.ASSESSMENT_DEPTS_CACHE_KEY)) {
            Object cacheMap = redisTemplate
                    .opsForValue().get(AssessmentConstants.ASSESSMENT_DEPTS_CACHE_KEY);
            if (cacheMap instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, List<Long>> deptCodeIdMap = (Map<String, List<Long>>) cacheMap;
                return deptCodeIdMap.values().stream().flatMap(List::stream).distinct().toList();
            } else {
                return Arrays.asList();
            }
        }
        return Arrays.asList();
    }

    /**
     * 通过部门编号获取需要考核的所有部门的id
     */
    public List<Long> getAssessmentUserIdsByDeptCode(String deptCode) {
        if (redisTemplate.hasKey(AssessmentConstants.ASSESSMENT_DEPTS_CACHE_KEY)) {
            Object cacheMap = redisTemplate
                    .opsForValue().get(AssessmentConstants.ASSESSMENT_DEPTS_CACHE_KEY);
            if (cacheMap instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, List<Long>> deptCodeIdMap = (Map<String, List<Long>>) cacheMap;
                return deptCodeIdMap.getOrDefault(deptCode, Arrays.asList()).stream().distinct()
                        .toList();
            } else {
                return Arrays.asList();
            }
        }
        return Arrays.asList();
    }

}
