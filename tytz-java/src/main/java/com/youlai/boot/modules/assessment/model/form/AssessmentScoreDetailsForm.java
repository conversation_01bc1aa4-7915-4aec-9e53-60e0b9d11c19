package com.youlai.boot.modules.assessment.model.form;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentItemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 履职考核得分细则表表单对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Schema(description = "履职考核得分细则表表单对象")
public class AssessmentScoreDetailsForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "得分细则ID")
    private Long id;

    @Schema(description = "得分归类")
    @NotNull(message = "得分归类不能为空")
    private AssessmentCategoryEnum category;

    @Schema(description = "得分类型")
    @NotNull(message = "得分类型不能为空")
    private AssessmentItemTypeEnum type;

    @Schema(description = "被考核人用户ID")
    @NotNull(message = "被考核人用户ID不能为空")
    private Long memberId;

    @Schema(description = "对应的考核规则ID")
    @NotNull(message = "对应的考核规则ID不能为空")
    private Long ruleId;

    @Schema(description = "得分对应的数据ID")
    @NotNull(message = "得分对应的数据ID不能为空")
    private Long dataId;

    @Schema(description = "备注")
    private String remark;
}
