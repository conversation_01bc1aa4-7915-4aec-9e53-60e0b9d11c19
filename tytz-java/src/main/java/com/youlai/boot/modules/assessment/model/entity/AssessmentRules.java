package com.youlai.boot.modules.assessment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核规则实体对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@TableName("tsz_assessment_rules")
public class AssessmentRules extends BaseEntity {

    /**
     * id 主键
     */
    private Long id;

    /**
     * 履职项目类别（ZSH: 总商会, XLH: 新联会）
     */
    private AssessmentCategoryEnum category;

    /**
     * 履职项目名称
     */
    private String ruleName;

    /**
     * 履职项目编号（ACTIVITY: 活动, MEETING: 会议, KEY_WORK: 重点工作, ENVIRONMENT: 营商环境问题报送,
     * OPINION: 意见建议, CHARITY: 公益慈善活动...）
     */
    private String ruleCode;

    /**
     * 父级规则ID
     */
    private Long parentId;

    /**
     * 父级规则路径
     */
    private String treePath;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 得分（非空，默认值为0，主分类为0，规则才有具体的分数）
     */
    private Integer score;

    /**
     * 得分上限（为null时无限制）
     */
    private Integer limitMax;

    /**
     * 得分下限（为null时无限制）
     */
    private Integer limitMin;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    private Integer isDeleted;
}
