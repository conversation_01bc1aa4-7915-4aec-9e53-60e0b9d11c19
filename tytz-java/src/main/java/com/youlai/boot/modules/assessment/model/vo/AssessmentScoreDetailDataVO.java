package com.youlai.boot.modules.assessment.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 履职考核得分对应数据列表详情
 */
@Getter
@Setter
@Schema(description = "履职考核得分对应数据列表详情试图对象")
public class AssessmentScoreDetailDataVO implements Serializable {

    @Schema(description = "得分对应数据的标题/名称/内容")
    private String dataTitle;

    @Schema(description = "得分对应的数据ID")
    private Long dataId;

    @Schema(description = "得分对应数类型翻译")
    private String dataTypeLabel;

    @Schema(description = "得分对应数据类型")
    private String dataType;

    @Schema(description = "具体得分")
    private Integer score;
}
