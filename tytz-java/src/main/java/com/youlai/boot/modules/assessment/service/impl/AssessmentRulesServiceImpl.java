package com.youlai.boot.modules.assessment.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.PostConstruct;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.assessment.converter.AssessmentRulesConverter;
import com.youlai.boot.modules.assessment.mapper.AssessmentRulesMapper;
import com.youlai.boot.modules.assessment.model.bo.AssessmentRuleCacheBO;
import com.youlai.boot.modules.assessment.model.dto.AssessmentRulesPageDTO;
import com.youlai.boot.modules.assessment.model.entity.AssessmentRules;
import com.youlai.boot.modules.assessment.model.form.AssessmentRulesForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentRulesQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesPageVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesVO;
import com.youlai.boot.modules.assessment.service.AssessmentRulesService;
import com.youlai.boot.modules.assessment.utils.AssessmentRulesCacheUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 履职考核规则服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssessmentRulesServiceImpl extends ServiceImpl<AssessmentRulesMapper, AssessmentRules>
        implements AssessmentRulesService {

    private final AssessmentRulesConverter assessmentRulesConverter;
    private final AssessmentRulesCacheUtils assessmentRulesCacheUtils;

    /**
     * 初始化履职规则缓存
     */
    @PostConstruct
    public void initAssessmentRulesCache() {
        assessmentRulesCacheUtils.initAssessmentRulesCache();
    }

    /**
     * 刷新履职规则缓存
     */
    @Override
    public void refreshAssessmentRulesCache() {
        assessmentRulesCacheUtils.refreshAllAssessmentRulesCache();
    }

    /**
     * 刷新某条履职评分规则
     */
    @Override
    public void refreshAssessmentRulesCache(AssessmentCategoryEnum category, String ruleCode) {
        assessmentRulesCacheUtils.refreshSingleAssessmentRuleCache(category, ruleCode);
    }

    /**
     * 获取单条履职规则缓存
     *
     * @description 用于获取单条履职规则缓存，如果缓存中没有则从数据库中获取并更新缓存
     *
     */
    @Override
    public AssessmentRuleCacheBO getAssessmentRuleCacheBO(AssessmentCategoryEnum category, String ruleCode) {
        return assessmentRulesCacheUtils.getAssessmentRuleCache(category, ruleCode);
    }

    /**
     * 履职考核规则分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    @Override
    public IPage<AssessmentRulesPageVO> getAssessmentRulesPage(AssessmentRulesQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<AssessmentRulesPageDTO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<AssessmentRulesPageDTO> result = this.baseMapper.getAssessmentRulesPage(page, queryParams);
        // 统计相同的category与itemName的个数
        result.getRecords().stream()
                .collect(Collectors.groupingBy(item -> Arrays.asList(item.getCategory(), item.getItemName())))
                .forEach((itemName, itemList) -> {
                    itemList.forEach(item -> item.setItemSpan(itemList.size()));
                });

        IPage<AssessmentRulesPageVO> voPage = result.convert(assessmentRulesConverter::toPageVO);
        return voPage;
    }

    /**
     * 履职考核规则列表
     *
     * @param queryParams 查询参数
     * @return 列表数据
     */
    @Override
    public List<AssessmentRulesPageVO> getAssessmentRules(AssessmentRulesQuery queryParams) {

        // 查询数据
        List<AssessmentRulesPageDTO> list = this.baseMapper.getAssessmentRulesPage(queryParams);
        // 统计相同的category与itemName的个数
        list.stream()
                .collect(Collectors.groupingBy(item -> Arrays.asList(item.getCategory(), item.getItemName())))
                .forEach((itemName, itemList) -> {
                    itemList.forEach(item -> item.setItemSpan(itemList.size()));
                });

        // 转换为VO
        return list.stream()
                .map(assessmentRulesConverter::toPageVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取履职考核规则树形列表
     *
     * @param queryParams 查询参数
     * @return 树形列表数据
     */
    @Override
    public List<AssessmentRulesVO> getAssessmentRulesTree(AssessmentRulesQuery queryParams) {
        // 构建查询条件
        LambdaQueryWrapper<AssessmentRules> queryWrapper = new LambdaQueryWrapper<AssessmentRules>()
                .eq(queryParams.getCategory() != null, AssessmentRules::getCategory, queryParams.getCategory())
                .like(StrUtil.isNotBlank(queryParams.getRuleName()), AssessmentRules::getRuleName,
                        queryParams.getRuleName())
                .like(StrUtil.isNotBlank(queryParams.getRuleCode()), AssessmentRules::getRuleCode,
                        queryParams.getRuleCode())
                .eq(queryParams.getParentId() != null, AssessmentRules::getParentId, queryParams.getParentId())
                .eq(AssessmentRules::getIsDeleted, 0)
                .orderByAsc(AssessmentRules::getSort);

        // 查询所有数据
        List<AssessmentRules> list = this.list(queryWrapper);

        // 转换为VO并构建树形结构
        List<AssessmentRulesVO> voList = list.stream()
                .map(assessmentRulesConverter::toVO)
                .collect(Collectors.toList());

        return buildTree(voList, 0L);
    }

    /**
     * 构建树形结构
     *
     * @param list     数据列表
     * @param parentId 父级ID
     * @return 树形结构数据
     */
    private List<AssessmentRulesVO> buildTree(List<AssessmentRulesVO> list, Long parentId) {
        return list.stream()
                .filter(item -> parentId.equals(item.getParentId()))
                .peek(item -> {
                    List<AssessmentRulesVO> children = buildTree(list, item.getId());
                    item.setChildren(children);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取履职考核规则表单数据
     *
     * @param id 规则ID
     * @return 表单数据
     */
    @Override
    public AssessmentRulesForm getAssessmentRulesFormData(Long id) {
        AssessmentRules entity = this.getById(id);
        return assessmentRulesConverter.toForm(entity);
    }

    /**
     * 新增履职考核规则
     *
     * @param formData 规则表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveAssessmentRules(AssessmentRulesForm formData) {
        // 表单数据转换为实体
        AssessmentRules entity = assessmentRulesConverter.toEntity(formData);

        // 设置创建人
        entity.setCreateBy(SecurityUtils.getUserId());

        // 保存实体
        boolean result = this.save(entity);

        // 同步更新缓存
        if (result) {
            assessmentRulesCacheUtils.refreshSingleAssessmentRuleCache(entity.getCategory(), entity.getRuleCode());
            log.debug("新增履职规则后已同步更新缓存: {}:{}", entity.getCategory().getValue(), entity.getRuleCode());
        }

        return result;
    }

    /**
     * 修改履职考核规则
     *
     * @param id       规则ID
     * @param formData 规则表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateAssessmentRules(Long id, AssessmentRulesForm formData) {
        // 获取原实体
        AssessmentRules existingEntity = this.getById(id);
        Assert.notNull(existingEntity, "履职考核规则不存在");

        // 表单数据转换为实体
        AssessmentRules entity = assessmentRulesConverter.toEntity(formData);
        entity.setId(id);

        // 设置更新人
        entity.setUpdateBy(SecurityUtils.getUserId());

        // 更新实体
        boolean result = this.updateById(entity);

        // 同步更新缓存
        if (result) {
            // 如果规则编号或类别发生变化，需要删除旧缓存
            if (!existingEntity.getRuleCode().equals(entity.getRuleCode()) ||
                    !existingEntity.getCategory().equals(entity.getCategory())) {
                assessmentRulesCacheUtils.removeAssessmentRuleCache(existingEntity.getCategory(),
                        existingEntity.getRuleCode());
            }
            // 更新新的缓存
            assessmentRulesCacheUtils.refreshSingleAssessmentRuleCache(entity.getCategory(), entity.getRuleCode());
            log.debug("更新履职规则后已同步更新缓存: {}:{}", entity.getCategory().getValue(), entity.getRuleCode());
        }

        return result;
    }

    /**
     * 删除履职考核规则
     *
     * @param ids 规则ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteAssessmentRules(String ids) {
        Assert.notBlank(ids, "删除的履职考核规则ID不能为空");

        // 获取要删除的实体信息，用于清除缓存
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        List<AssessmentRules> entitiesToDelete = this.listByIds(idList);

        // 逻辑删除
        boolean result = this.removeByIds(idList);

        // 同步删除缓存
        if (result) {
            for (AssessmentRules entity : entitiesToDelete) {
                assessmentRulesCacheUtils.removeAssessmentRuleCache(entity.getCategory(), entity.getRuleCode());
                log.debug("删除履职规则后已同步删除缓存: {}:{}", entity.getCategory().getValue(), entity.getRuleCode());
            }
        }

        return result;
    }
}
