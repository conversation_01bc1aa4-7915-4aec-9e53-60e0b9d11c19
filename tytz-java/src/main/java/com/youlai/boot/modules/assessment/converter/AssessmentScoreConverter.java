package com.youlai.boot.modules.assessment.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScore;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreForm;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 履职考核得分总表对象转换器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper(componentModel = "spring")
public interface AssessmentScoreConverter {

    /**
     * 实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    @Mapping(target = "categoryLabel", expression = "java(entity.getCategory() != null ? entity.getCategory().getLabel() : null)")
    @Mapping(target = "memberName", ignore = true)
    @Mapping(target = "memberDeptName", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "activityCount", ignore = true)
    @Mapping(target = "meetingCount", ignore = true)
    @Mapping(target = "keyWorkCount", ignore = true)
    @Mapping(target = "environmentCount", ignore = true)
    @Mapping(target = "opinionCount", ignore = true)
    AssessmentScoreVO toVO(AssessmentScore entity);

    /**
     * 分页实体转换为分页VO
     *
     * @param page 分页实体对象
     * @return 分页VO对象
     */
    @Mapping(target = "countId", ignore = true)
    @Mapping(target = "maxLimit", ignore = true)
    @Mapping(target = "optimizeCountSql", ignore = true)
    @Mapping(target = "optimizeJoinOfCountSql", ignore = true)
    @Mapping(target = "orders", ignore = true)
    @Mapping(target = "searchCount", ignore = true)
    Page<AssessmentScoreVO> toPageVO(Page<AssessmentScore> page);

    /**
     * 表单转换为实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    AssessmentScore toEntity(AssessmentScoreForm form);

    /**
     * 实体转换为表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    @InheritInverseConfiguration(name = "toEntity")
    AssessmentScoreForm toForm(AssessmentScore entity);
}
