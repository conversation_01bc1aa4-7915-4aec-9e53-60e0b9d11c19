package com.youlai.boot.modules.assessment.model.form;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 履职考核得分总表表单对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Schema(description = "履职考核得分总表表单对象")
public class AssessmentScoreForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "得分ID")
    private Long id;

    @Schema(description = "得分归类")
    @NotNull(message = "得分归类不能为空")
    private AssessmentCategoryEnum category;

    @Schema(description = "被考核人用户ID")
    @NotNull(message = "被考核人用户ID不能为空")
    private Long memberId;

    @Schema(description = "年度")
    @NotNull(message = "年度不能为空")
    private Integer year;

    @Schema(description = "活动得分")
    private Integer activityScore;

    @Schema(description = "会议得分")
    private Integer meetingScore;

    @Schema(description = "重点工作得分")
    private Integer keyWorkScore;

    @Schema(description = "营商环境问题报送得分")
    private Integer environmentScore;

    @Schema(description = "意见建议得分")
    private Integer opinionScore;

    @Schema(description = "总得分")
    private Integer score;

    @Schema(description = "备注")
    private String remark;
}
