package com.youlai.boot.modules.assessment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.modules.assessment.model.bo.AssessmentRuleCacheBO;
import com.youlai.boot.modules.assessment.model.entity.AssessmentRules;
import com.youlai.boot.modules.assessment.model.form.AssessmentRulesForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentRulesQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesPageVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesVO;

import java.util.List;

/**
 * 履职考核规则服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface AssessmentRulesService extends IService<AssessmentRules> {

    /**
     * 刷新所有履职评分规则缓存
     */
    void refreshAssessmentRulesCache();

    /**
     * 刷新某条履职评分规则（唯一性确认条件: category:ruleCode）
     */
    void refreshAssessmentRulesCache(AssessmentCategoryEnum category, String ruleCode);

    /**
     * 获取单条履职评分规则
     * 
     * @param category 履职项目类别
     * @param ruleCode 履职规则编号
     * @return 履职评分规则缓存对象
     */
    AssessmentRuleCacheBO getAssessmentRuleCacheBO(AssessmentCategoryEnum category, String ruleCode);

    /**
     * 履职考核规则分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    IPage<AssessmentRulesPageVO> getAssessmentRulesPage(AssessmentRulesQuery queryParams);

    /**
     * 履职考核规则列表
     *
     * @param queryParams 查询参数
     * @return 列表数据
     */
    List<AssessmentRulesPageVO> getAssessmentRules(AssessmentRulesQuery queryParams);

    /**
     * 获取履职考核规则树形列表
     *
     * @param queryParams 查询参数
     * @return 树形列表数据
     */
    List<AssessmentRulesVO> getAssessmentRulesTree(AssessmentRulesQuery queryParams);

    /**
     * 获取履职考核规则表单数据
     *
     * @param id 规则ID
     * @return 表单数据
     */
    AssessmentRulesForm getAssessmentRulesFormData(Long id);

    /**
     * 新增履职考核规则
     *
     * @param formData 规则表单对象
     * @return 是否成功
     */
    boolean saveAssessmentRules(AssessmentRulesForm formData);

    /**
     * 修改履职考核规则
     *
     * @param id       规则ID
     * @param formData 规则表单对象
     * @return 是否成功
     */
    boolean updateAssessmentRules(Long id, AssessmentRulesForm formData);

    /**
     * 删除履职考核规则
     *
     * @param ids 规则ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteAssessmentRules(String ids);
}
