package com.youlai.boot.modules.assessment.model.vo;

import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 履职考核得分总表视图对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Schema(description = "履职考核得分总表视图对象")
public class AssessmentScoreVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "得分ID")
    private Long id;

    @Schema(description = "得分归类")
    private AssessmentCategoryEnum category;

    @Schema(description = "得分归类标签")
    private String categoryLabel;

    @Schema(description = "被考核人用户ID")
    private Long memberId;

    @Schema(description = "会员名称（用户名）")
    private String memberName;

    @Schema(description = "会员所属商会（用户部门）")
    private String memberDeptName;

    @Schema(description = "所属单位（用户表company字段）")
    private String company;

    @Schema(description = "年度")
    private Integer year;

    @Schema(description = "参加活动次数")
    private Long activityCount;

    @Schema(description = "活动得分")
    private Integer activityScore;

    @Schema(description = "参加会议次数")
    private Long meetingCount;

    @Schema(description = "会议得分")
    private Integer meetingScore;

    @Schema(description = "年度重点工作次数")
    private Long keyWorkCount;

    @Schema(description = "重点工作得分")
    private Integer keyWorkScore;

    @Schema(description = "营商环境问题报送数量")
    private Long environmentCount;

    @Schema(description = "营商环境问题报送得分")
    private Integer environmentScore;

    @Schema(description = "意见建议数量")
    private Long opinionCount;

    @Schema(description = "意见建议得分")
    private Integer opinionScore;

    @Schema(description = "总得分")
    private Integer score;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
