package com.youlai.boot.modules.assessment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.youlai.boot.common.result.PageResult;
import com.youlai.boot.common.result.Result;
import com.youlai.boot.modules.assessment.model.form.AssessmentRulesForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentRulesQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesPageVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentRulesVO;
import com.youlai.boot.modules.assessment.service.AssessmentRulesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 履职考核规则前端控制层
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Tag(name = "履职考核规则接口")
@RestController
@RequestMapping("/api/v1/assessment-rules")
@RequiredArgsConstructor
public class AssessmentRulesController {

    private final AssessmentRulesService assessmentRulesService;

    @Operation(summary = "履职考核规则分页列表")
    @GetMapping("/page")
    public PageResult<AssessmentRulesPageVO> getAssessmentRulesPage(@Valid AssessmentRulesQuery queryParams) {
        IPage<AssessmentRulesPageVO> result = assessmentRulesService.getAssessmentRulesPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "履职考核规则列表")
    @GetMapping("/list")
    public Result<List<AssessmentRulesPageVO>> getAssessmentRules(@Valid AssessmentRulesQuery queryParams) {
        List<AssessmentRulesPageVO> result = assessmentRulesService.getAssessmentRules(queryParams);
        return Result.success(result);
    }

    @Operation(summary = "履职考核规则树形列表")
    @GetMapping("/tree")
    public Result<List<AssessmentRulesVO>> getAssessmentRulesTree(@Valid AssessmentRulesQuery queryParams) {
        List<AssessmentRulesVO> result = assessmentRulesService.getAssessmentRulesTree(queryParams);
        return Result.success(result);
    }

    @Operation(summary = "新增履职考核规则")
    @PostMapping
    public Result<Void> saveAssessmentRules(@RequestBody @Valid AssessmentRulesForm formData) {
        boolean result = assessmentRulesService.saveAssessmentRules(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取履职考核规则表单数据")
    @GetMapping("/{id}/form")
    public Result<AssessmentRulesForm> getAssessmentRulesForm(
            @Parameter(description = "规则ID") @PathVariable Long id) {
        AssessmentRulesForm formData = assessmentRulesService.getAssessmentRulesFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "修改履职考核规则")
    @PutMapping(value = "/{id}")
    public Result<Void> updateAssessmentRules(
            @Parameter(description = "规则ID") @PathVariable Long id,
            @RequestBody @Validated AssessmentRulesForm formData) {
        boolean result = assessmentRulesService.updateAssessmentRules(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除履职考核规则")
    @DeleteMapping("/{ids}")
    public Result<Void> deleteAssessmentRules(
            @Parameter(description = "规则ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        boolean result = assessmentRulesService.deleteAssessmentRules(ids);
        return Result.judge(result);
    }
}
