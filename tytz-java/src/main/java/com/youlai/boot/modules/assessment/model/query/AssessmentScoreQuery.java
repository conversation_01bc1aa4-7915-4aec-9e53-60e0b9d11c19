package com.youlai.boot.modules.assessment.model.query;

import java.util.List;

import com.youlai.boot.common.base.BasePageQuery;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核得分总表查询对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Schema(description = "履职考核得分总表查询对象")
@Getter
@Setter
public class AssessmentScoreQuery extends BasePageQuery {

    @Schema(description = "得分归类")
    private AssessmentCategoryEnum category;

    @Schema(description = "被考核人用户ID")
    private Long memberId;

    @Schema(description = "会员名称")
    private String memberName;

    @Schema(description = "年度")
    private Integer year;

    @Schema(description = "所属单位")
    private String company;

    @Schema(description = "被考核用户id列表")
    private List<Long> userIds;
}
