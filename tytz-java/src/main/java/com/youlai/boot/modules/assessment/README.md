# Assessment 模块说明

## 模块概述

Assessment 模块是履职考核系统的核心模块，负责管理考核规则、考核得分总表和得分细则。

## 模块结构

```
assessment/
├── controller/          # 控制器层
│   ├── AssessmentRulesController.java
│   ├── AssessmentScoreController.java
│   └── AssessmentScoreDetailsController.java
├── converter/           # 对象转换器
│   ├── AssessmentRulesConverter.java
│   ├── AssessmentScoreConverter.java
│   └── AssessmentScoreDetailsConverter.java
├── mapper/             # 数据访问层
│   ├── AssessmentRulesMapper.java
│   ├── AssessmentScoreMapper.java
│   └── AssessmentScoreDetailsMapper.java
├── model/              # 模型类
│   ├── entity/         # 实体类
│   │   ├── AssessmentRules.java
│   │   ├── AssessmentScore.java
│   │   └── AssessmentScoreDetails.java
│   ├── vo/             # 视图对象
│   │   ├── AssessmentRulesVO.java
│   │   ├── AssessmentScoreVO.java
│   │   └── AssessmentScoreDetailsVO.java
│   ├── query/          # 查询对象
│   │   ├── AssessmentRulesQuery.java
│   │   ├── AssessmentScoreQuery.java
│   │   └── AssessmentScoreDetailsQuery.java
│   └── form/           # 表单对象
│       ├── AssessmentRulesForm.java
│       ├── AssessmentScoreForm.java
│       └── AssessmentScoreDetailsForm.java
└── service/            # 业务逻辑层
    ├── AssessmentRulesService.java
    ├── AssessmentScoreService.java
    ├── AssessmentScoreDetailsService.java
    └── impl/
        ├── AssessmentRulesServiceImpl.java
        ├── AssessmentScoreServiceImpl.java
        └── AssessmentScoreDetailsServiceImpl.java
```

## 核心功能

### 1. 考核规则管理 (AssessmentRules)

- **功能**: 管理履职考核规则，支持树形结构
- **主要接口**:
  - `GET /api/v1/assessment-rules/page` - 分页查询考核规则
  - `GET /api/v1/assessment-rules/tree` - 获取树形结构的考核规则
  - `POST /api/v1/assessment-rules` - 新增考核规则
  - `PUT /api/v1/assessment-rules/{id}` - 修改考核规则
  - `DELETE /api/v1/assessment-rules/{ids}` - 删除考核规则

### 2. 考核得分总表 (AssessmentScore)

- **功能**: 管理用户的年度考核总得分，包含各类型得分统计
- **主要接口**:
  - `GET /api/v1/assessment-score/page` - 分页查询考核得分总表
  - `POST /api/v1/assessment-score` - 新增考核得分记录
  - `PUT /api/v1/assessment-score/{id}` - 修改考核得分记录
  - `DELETE /api/v1/assessment-score/{ids}` - 删除考核得分记录

### 3. 考核得分细则 (AssessmentScoreDetails)

- **功能**: 管理具体的得分细则，支持自动同步更新总得分
- **主要接口**:
  - `GET /api/v1/assessment-score-details/page` - 分页查询得分细则
  - `GET /api/v1/assessment-score-details/member/{memberId}` - 获取用户得分细则详情
  - `POST /api/v1/assessment-score-details` - 新增得分细则
  - `PUT /api/v1/assessment-score-details/{id}` - 修改得分细则
  - `DELETE /api/v1/assessment-score-details/{ids}` - 删除得分细则

## 核心枚举

### AssessmentCategoryEnum (考核类别)
- `ZSH` - 总商会
- `XLH` - 新联会

### AssessmentRuleTypeEnum (考核规则类型)
二级枚举结构，包含一级分类和二级细分：

**一级分类**:
- `MEETING` - 参加会议
- `ACTIVITY` - 参加活动
- `KEY_WORK` - 参加年度重点工作
- `ENVIRONMENT` - 营商环境问题报送
- `OPINION` - 意见征集汇总报送
- `CHARITY` - 办好事、解难题、做公益情况

**二级细分** (示例):
- `MEETING:MEMBER` - 参加会员（代表）大会
- `MEETING:PRESIDENT` - 参加会长会议
- `ACTIVITY:RESEARCH` - 参加调研、视察、考察等活动
- 等等...

## 核心特性

### 1. 自动得分同步
当新增、修改或删除得分细则时，系统会自动同步更新对应的得分总表：
- 支持事务管理，确保数据一致性
- 考虑得分的正负值
- 支持上下限约束（规则本身和父级规则的约束）

### 2. 复杂查询支持
- 支持用户部门信息关联查询
- 支持各类型得分次数统计
- 支持多表关联的数据标题显示

### 3. 树形结构支持
考核规则支持树形结构，便于分层管理和展示。

## 数据库表结构

### tsz_assessment_rules (考核规则表)
- 支持树形结构 (parent_id, tree_path)
- 包含得分配置 (score, limit_max, limit_min)
- 支持排序 (sort)

### tsz_assessment_score (考核得分总表)
- 按用户、年度、类别维度统计
- 分类型存储得分 (activity_score, meeting_score 等)
- 包含总得分字段 (score)

### tsz_assessment_score_details (考核得分细则表)
- 记录具体的得分明细
- 关联考核规则 (rule_id)
- 关联具体数据 (data_id)

## 使用示例

### 获取用户2024年度得分细则详情
```http
GET /api/v1/assessment-score-details/member/123?year=2024
```

### 分页查询总商会的考核得分
```http
GET /api/v1/assessment-score/page?category=ZSH&pageNum=1&pageSize=10
```

### 新增得分细则（会自动同步更新总得分）
```http
POST /api/v1/assessment-score-details
Content-Type: application/json

{
  "category": "ZSH",
  "type": "MEETING:MEMBER",
  "memberId": 123,
  "ruleId": 456,
  "dataId": 789,
  "remark": "参加2024年会员大会"
}
```

## 注意事项

1. **事务管理**: 得分细则的增删改操作都包含在事务中，确保与总得分的同步更新
2. **上下限约束**: 目前实现了基础的约束框架，具体的约束逻辑可根据业务需求进一步完善
3. **数据关联**: XML中的关联查询假设了相关表的存在，如活动表等，需要根据实际情况调整
4. **性能优化**: 对于大量数据的场景，建议添加适当的索引和缓存机制
