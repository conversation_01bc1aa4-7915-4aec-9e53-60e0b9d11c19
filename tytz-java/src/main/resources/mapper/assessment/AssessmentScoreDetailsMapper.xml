<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.assessment.mapper.AssessmentScoreDetailsMapper">
    <!-- 获取履职考核得分细则表分页数据 -->
    <select id="getAssessmentScoreDetailsPage" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO">
        SELECT
            sd.id,
            sd.category,
            sd.type,
            sd.member_id,
            u.nickname AS member_name,
            sd.rule_id,
            r.rule_name,
            r.score AS rule_score,
            sd.data_id,
            sd.remark,
            sd.create_time,
            sd.update_time,
            -- 根据不同类型获取数据标题
            CASE 
                WHEN sd.type LIKE 'MEETING%' THEN m.title
                WHEN sd.type LIKE 'ACTIVITY%' THEN a.title
                WHEN sd.type LIKE 'OPINION%' THEN o.title
                ELSE CONCAT('数据ID: ', sd.data_id)
            END AS data_title
        FROM
            tsz_assessment_score_details sd
        LEFT JOIN sys_user u ON sd.member_id = u.id
        LEFT JOIN tsz_assessment_rules r ON sd.rule_id = r.id
        -- 关联会议表
        LEFT JOIN tsz_meeting m ON sd.data_id = m.id AND sd.type LIKE 'MEETING%'
        -- 关联活动表（假设存在活动表）
        LEFT JOIN tsz_activity a ON sd.data_id = a.id AND sd.type LIKE 'ACTIVITY%'
        -- 关联意见建议表
        LEFT JOIN tsz_opinion o ON sd.data_id = o.id AND sd.type LIKE 'OPINION%'
        WHERE 1 = 1
        <if test="queryParams.category != null">
            AND sd.category = #{queryParams.category}
        </if>
        <if test="queryParams.type != null">
            AND sd.type = #{queryParams.type}
        </if>
        <if test="queryParams.memberId != null">
            AND sd.member_id = #{queryParams.memberId}
        </if>
        <if test="queryParams.memberName != null and queryParams.memberName != ''">
            AND u.nickname LIKE CONCAT('%', #{queryParams.memberName}, '%')
        </if>
        <if test="queryParams.ruleId != null">
            AND sd.rule_id = #{queryParams.ruleId}
        </if>
        <if test="queryParams.dataId != null">
            AND sd.data_id = #{queryParams.dataId}
        </if>
        <if test="queryParams.year != null">
            AND YEAR(sd.create_time) = #{queryParams.year}
        </if>
        ORDER BY sd.create_time DESC
    </select>
    <!-- 获取用户的得分细则详情 -->
    <select id="getMemberScoreDetails" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO">
        SELECT
            sd.id,
            sd.category,
            sd.type,
            sd.member_id,
            u.nickname AS member_name,
            sd.rule_id,
            r.rule_name,
            r.score AS rule_score,
            sd.data_id,
            sd.remark,
            sd.create_time,
            sd.update_time,
            -- 根据不同类型获取数据标题
            CASE 
                WHEN sd.type LIKE 'MEETING%' THEN m.title
                WHEN sd.type LIKE 'ACTIVITY%' THEN a.title
                WHEN sd.type LIKE 'OPINION%' THEN o.title
                ELSE CONCAT('数据ID: ', sd.data_id)
            END AS data_title
        FROM
            tsz_assessment_score_details sd
        LEFT JOIN sys_user u ON sd.member_id = u.id
        LEFT JOIN tsz_assessment_rules r ON sd.rule_id = r.id
        -- 关联会议表
        LEFT JOIN tsz_meeting m ON sd.data_id = m.id AND sd.type LIKE 'MEETING%'
        -- 关联活动表（假设存在活动表）
        LEFT JOIN tsz_activity a ON sd.data_id = a.id AND sd.type LIKE 'ACTIVITY%'
        -- 关联意见建议表
        LEFT JOIN tsz_opinion o ON sd.data_id = o.id AND sd.type LIKE 'OPINION%'
        WHERE 1 = 1
            AND sd.member_id = #{memberId}
            AND YEAR(sd.create_time) = #{year}
        ORDER BY sd.type, r.sort, sd.create_time DESC
    </select>
    <!-- getAssessmentScoreDetailActivityData -->
    <select id="getAssessmentScoreDetailActivityData" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO">
        SELECT
        	tat.title AS data_title,
        	tat.activity_type AS data_type,
        	tasd.category,
        	tat.content AS data_content,
        	SUM(tar.score) AS score
        FROM
        	tsz_assessment_score_details tasd
        	LEFT JOIN tsz_activity tat ON tat.id = tasd.data_id 
        	LEFT JOIN tsz_assessment_rules tar ON tar.id = tasd.rule_id
        WHERE
        	tat.is_deleted = 0 
        	AND tasd.category = #{queryParams.category}
            AND tasd.type = #{queryParams.dataType}
        <!-- AND tasd.type = 'ACTIVITY' -->
        	AND tasd.member_id = #{queryParams.memberId} 
            AND tat.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	AND tat.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        	GROUP BY tasd.category, tasd.member_id, tasd.type, tasd.data_id, tat.title, tat.activity_type, tat.content
        	ORDER BY tat.start_time ASC;
    </select>
    <!-- getAssessmentScoreDetailMeetingData -->
    <select id="getAssessmentScoreDetailMeetingData" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO">
        SELECT
        	tm.title AS data_title,
        	tm.meeting_type AS data_type,
        	tasd.category,
        	tm.content AS data_content,
        	SUM(tar.score) AS score
        FROM
        	tsz_assessment_score_details tasd
        	LEFT JOIN tsz_meeting tm ON tm.id = tasd.data_id 
        	LEFT JOIN tsz_assessment_rules tar ON tar.id = tasd.rule_id
        WHERE
        	tm.is_deleted = 0 
        	AND tasd.category = #{queryParams.category}
            AND tasd.type = #{queryParams.dataType}
        <!-- AND tasd.type = 'MEETING' -->
        	AND tasd.member_id = #{queryParams.memberId} 
        	AND tm.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	AND tm.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        	GROUP BY tasd.category, tasd.member_id, tasd.type, tasd.data_id, tm.title, tm.meeting_type, tm.content
        	ORDER BY tm.start_time ASC;
    </select>
    <!-- getAssessmentScoreDetailKeyWorkData -->
    <select id="getAssessmentScoreDetailKeyWorkData" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO">
        SELECT
        	tkw.work_name AS data_title,
        	tkw.work_type AS data_type,
        	tasd.category,
        	tkw.work_content AS data_content,
        	SUM(tar.score) AS score
        FROM
        	tsz_assessment_score_details tasd
        	LEFT JOIN tsz_key_work tkw ON tkw.id = tasd.data_id 
        	LEFT JOIN tsz_assessment_rules tar ON tar.id = tasd.rule_id
        WHERE
        	tkw.is_deleted = 0 
        	AND tasd.category = #{queryParams.category}
            AND tasd.type = #{queryParams.dataType}
        <!-- AND tasd.type = 'KEY_WORK' -->
        	AND tasd.member_id = #{queryParams.memberId} 
        	AND tkw.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	AND tkw.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        	GROUP BY tasd.category, tasd.member_id, tasd.type, tasd.data_id, tkw.work_name, tkw.work_type, tkw.work_content
        	ORDER BY tkw.start_time ASC;
    </select>
    <!-- getAssessmentScoreDetailEnvironmentData -->
    <select id="getAssessmentScoreDetailEnvironmentData" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO">
        SELECT
        	tb.title AS data_title,
        	tb.business_type AS data_type,
        	tasd.category,
        	tb.content AS data_content,
        	SUM(tar.score) AS score
        FROM
        	tsz_assessment_score_details tasd
        	LEFT JOIN tsz_problem tb ON tb.id = tasd.data_id 
        	LEFT JOIN tsz_assessment_rules tar ON tar.id = tasd.rule_id
        WHERE
        	tb.is_deleted = 0 
            AND tasd.category = #{queryParams.category}
            AND tasd.type = #{queryParams.dataType}
        <!-- AND tasd.type = 'ENVIRONMENT'  -->
        	AND tasd.member_id = #{queryParams.memberId} 
        	AND tb.submit_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	AND tb.submit_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        	GROUP BY tasd.category, tasd.member_id, tasd.type, tasd.data_id, tb.title, tb.business_type, tb.content
        	ORDER BY tb.submit_time ASC;
    </select>
    <!-- getAssessmentScoreDetailOpinionData -->
    <select id="getAssessmentScoreDetailOpinionData" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO">
        SELECT
        	tot.content AS data_title,
        	tasd.category,
        	tot.content AS data_content,
        	SUM(tar.score) AS score
        FROM
        	tsz_assessment_score_details tasd
        	LEFT JOIN tsz_opinion tot ON tot.id = tasd.data_id 
        	LEFT JOIN tsz_assessment_rules tar ON tar.id = tasd.rule_id
        WHERE
        	tot.is_deleted = 0 
        	AND tasd.category = #{queryParams.category}
            AND tasd.type = #{queryParams.dataType}
        <!-- AND tasd.type = 'OPINION'  -->
        	AND tasd.member_id = #{queryParams.memberId} 
        	AND tot.create_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	AND tot.create_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        	GROUP BY tasd.category, tasd.member_id, tasd.type, tasd.data_id, tot.content
        	ORDER BY tot.create_time ASC;
    </select>
    <!-- getAssessmentScoreDetailCharityData -->
    <select id="getAssessmentScoreDetailCharityData" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO">
        SELECT
        	tat.title AS data_title,
        	tat.activity_type AS data_type,
        	tasd.category,
        	tat.content AS data_content,
        	SUM(tar.score) AS score
        FROM
        	tsz_assessment_score_details tasd
        	LEFT JOIN tsz_activity tat ON tat.id = tasd.data_id 
        	LEFT JOIN tsz_assessment_rules tar ON tar.id = tasd.rule_id
        WHERE
        	tat.is_deleted = 0 
        	AND tasd.category = #{queryParams.category}
            AND tasd.type = #{queryParams.dataType}
        <!-- AND tasd.type = 'ACTIVITY' -->
            AND tat.activity_type = 'CONTRIBUTION'
        	AND tasd.member_id = #{queryParams.memberId} 
            AND tat.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	AND tat.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        	GROUP BY tasd.category, tasd.member_id, tasd.type, tasd.data_id, tat.title, tat.activity_type, tat.content
        	ORDER BY tat.start_time ASC;
    </select>
</mapper>