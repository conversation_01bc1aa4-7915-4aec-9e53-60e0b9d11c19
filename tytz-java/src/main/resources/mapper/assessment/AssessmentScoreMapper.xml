<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.assessment.mapper.AssessmentScoreMapper">
    <!-- 获取履职考核得分总表分页数据 -->
    <select id="getAssessmentScorePage" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO">
        SELECT
            s.id,
            s.category,
            s.member_id,
            u.nickname AS member_name,
            u.company,
            s.year,
            s.activity_score,
            s.meeting_score,
            s.key_work_score,
            s.environment_score,
            s.opinion_score,
            s.score,
            s.remark,
            s.create_time,
            s.update_time,
            -- 统计各类型的次数
            COALESCE(activity_count.count, 0) AS activity_count,
            COALESCE(meeting_count.count, 0) AS meeting_count,
            COALESCE(key_work_count.count, 0) AS key_work_count,
            COALESCE(environment_count.count, 0) AS environment_count,
            COALESCE(opinion_count.count, 0) AS opinion_count
        FROM
            sys_user su
        LEFT JOIN tsz_assessment_score s ON su.id = s.member_id
        <!-- LEFT JOIN sys_user u ON s.member_id = u.id -->
        -- 统计活动次数
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tat.start_time) AS year
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_activity tat ON tat.id = t.data_id
                 WHERE tat.is_deleted = 0 AND t.type = 'ACTIVITY'
        <if test="queryParams.category != null and queryParams.category != ''">
            	AND tasd.category = #{queryParams.category}
        </if>
        <if test="queryParams.year != null and queryParams.year != ''">
                AND tat.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	    AND tat.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
	        GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) activity_count ON s.member_id = activity_count.member_id AND s.year = activity_count.year AND s.category = activity_count.category 
        -- 统计会议次数
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tm.start_time) AS year
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_meeting tm ON tm.id = t.data_id
                 WHERE tm.is_deleted = 0 AND t.type = 'MEETING'
        <if test="queryParams.category != null and queryParams.category != ''">
            	AND tasd.category = #{queryParams.category}
        </if>
        <if test="queryParams.year != null and queryParams.year != ''">
                AND tm.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	    AND tm.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) meeting_count ON s.member_id = meeting_count.member_id AND s.year = meeting_count.year AND s.category = meeting_count.category 
        -- 统计重点工作次数
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tkw.start_time) AS year
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_key_work tkw ON tkw.id = t.data_id
                 WHERE tkw.is_deleted = 0 AND t.type = 'KEY_WORK'
        <if test="queryParams.category != null and queryParams.category != ''">
            	AND tasd.category = #{queryParams.category}
        </if>
        <if test="queryParams.year != null and queryParams.year != ''">
  	            AND tkw.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	    AND tkw.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) key_work_count ON s.member_id = key_work_count.member_id AND s.year = key_work_count.year AND s.category = key_work_count.category 
        -- 统计营商环境问题报送数量
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tb.submit_time) AS YEAR
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_problem tb ON tb.id = t.data_id
                 WHERE tb.is_deleted = 0 AND t.type = 'ENVIRONMENT'
        <if test="queryParams.category != null and queryParams.category != ''">
            	AND tasd.category = #{queryParams.category}
        </if>
        <if test="queryParams.year != null and queryParams.year != ''">
  	            AND tb.submit_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	    AND tb.submit_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) environment_count ON s.member_id = environment_count.member_id AND s.year = environment_count.year AND s.category = environment_count.category 
        -- 统计意见建议数量
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tot.create_time) AS YEAR
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_opinion tot ON tot.id = t.data_id
                 WHERE tot.is_deleted = 0 AND t.type = 'OPINION'
        <if test="queryParams.category != null and queryParams.category != ''">
            	AND tasd.category = #{queryParams.category}
        </if>
        <if test="queryParams.year != null and queryParams.year != ''">
  	            AND tot.create_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	    AND tot.create_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) opinion_count ON s.member_id = opinion_count.member_id AND s.year = opinion_count.year AND s.category = opinion_count.category 
        WHERE 1 = 1
        <if test="queryParams.category != null">
            AND s.category = #{queryParams.category}
        </if>
        <if test="queryParams.memberId != null">
            AND s.member_id = #{queryParams.memberId}
        </if>
        <if test="queryParams.memberName != null and queryParams.memberName != ''">
            AND u.nickname LIKE CONCAT('%', #{queryParams.memberName}, '%')
        </if>
        <if test="queryParams.year != null">
            AND s.year = #{queryParams.year}
        </if>
        GROUP BY 
                s.id,
	            s.category,
	            s.member_id,
	            u.nickname,
	            u.company,
	            s.YEAR,
	            s.activity_score,
	            s.meeting_score,
	            s.key_work_score,
	            s.environment_score,
	            s.opinion_score,
	            s.score,
	            s.remark,
	            s.create_time,
	            s.update_time,
	            activity_count.count,
	            meeting_count.count,
	            key_work_count.count,
	            environment_count.count,
	            opinion_count.count 
        ORDER BY 
                s.score DESC, s.year DESC, s.create_time DESC
    </select>
</mapper>