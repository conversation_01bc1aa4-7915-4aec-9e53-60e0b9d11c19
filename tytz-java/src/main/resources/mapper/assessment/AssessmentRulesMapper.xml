<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.assessment.mapper.AssessmentRulesMapper">
	<!-- 获取履职考核规则分页数据 -->
	<select id="getAssessmentRulesPage" resultType="com.youlai.boot.modules.assessment.model.dto.AssessmentRulesPageDTO">
        SELECT
        	c.id AS id,
        	p.rule_name AS item_name,
        	p.rule_code AS item_code,
        	c.rule_name AS rule_name,
        	c.rule_code AS rule_code,
        	c.category AS category,
        	c.parent_id AS parent_id,
        	c.tree_path AS tree_path,
        	c.sort AS sort,
        	c.score AS score,
        	c.limit_max AS limit_max,
        	c.limit_min AS limit_min
        FROM
        	tsz_assessment_rules p
        	LEFT JOIN tsz_assessment_rules c ON p.id = c.parent_id 
        WHERE
        	p.is_deleted = 0 
        	AND c.is_deleted = 0 
        	AND p.parent_id = 0
		<if test="queryParams.category != null">
            AND c.category = #{queryParams.category}
        </if>
		<if test="queryParams.ruleName != null and queryParams.ruleName != ''">
            AND p.rule_name LIKE CONCAT('%', #{queryParams.ruleName}, '%')
        </if>
		<if test="queryParams.ruleCode != null and queryParams.ruleCode != ''">
            AND p.rule_code LIKE CONCAT('%', #{queryParams.ruleCode}, '%')
        </if>
        ORDER BY p.category ASC, c.category ASC, p.sort ASC, c.sort ASC
	</select>
</mapper>