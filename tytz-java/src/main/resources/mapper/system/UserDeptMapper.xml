<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.system.mapper.UserDeptMapper">
    <!-- deleteByDeptIds -->
    <delete id="deleteByDeptIds">
        DELETE FROM sys_user_dept WHERE dept_id IN
        <foreach item="item" index="index" collection="deptIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <!-- deleteByUserIds -->
    <delete id="deleteByUserIds">
        DELETE FROM sys_user_dept WHERE user_id IN
        <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <!-- 通过用户id获取部门id集合 -->
    <select id="selectDeptIdsByUserId" resultType="java.lang.Long">
        SELECT sud.dept_id FROM sys_user_dept sud WHERE sud.user_id = #{userId}
    </select>
    <!-- deleteByUserIdsAndDeptIds -->
    <delete id="deleteByUserIdsAndDeptIds">
        DELETE FROM sys_user_dept WHERE 1 = 1
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            AND dept_id IN
            <foreach item="item" index="index" collection="deptIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>
</mapper>