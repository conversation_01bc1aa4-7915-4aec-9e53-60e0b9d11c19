/**
 * 部门类型枚举以及字典配置
 */

import { dictsToMap } from "@/utils/dicts";

/** 部门类型枚举 */
export enum DeptTypeEnum {
  /** 部门 */
  DEPT = "DEPT",
  /** 职务 */
  POSITION = "POSITION",
}

/** 部门类型字典数据 */
export const DeptTypeDicts: Dict<DeptTypeEnum>[] = [
  { value: DeptTypeEnum.DEPT, label: "部门", sort: 1 },
  { value: DeptTypeEnum.POSITION, label: "职务", sort: 2 },
];

/** 部门类型字典数据Map */
export const DeptTypeDictMap = dictsToMap(DeptTypeDicts);
