import { dictsToMap } from "@/utils/dicts";

/**
 * 履职考核分类枚举
 */
export enum AssessmentCategoryEnum {
  /** 总商会 */
  ZSH = "ZSH",
  /** 新联会 */
  XLH = "XLH",
}

/**
 * 履职考核分类字典数据
 */
export const AssessmentCategoryDicts: Dict<AssessmentCategoryEnum>[] = [
  { value: AssessmentCategoryEnum.ZSH, label: "总商会", sort: 1 },
  { value: AssessmentCategoryEnum.XLH, label: "新联会", sort: 2 },
];

/**
 * 履职考核分类字典数据Map
 */
export const AssessmentCategoryDictMap = dictsToMap(AssessmentCategoryDicts);
