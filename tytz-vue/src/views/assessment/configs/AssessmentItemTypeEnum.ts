import { dictsToMap } from "@/utils/dicts";

/**
 * 履职考核项目类型枚举
 */
export enum AssessmentItemTypeEnum {
  /** 参加活动  */
  ACTIVITY = "ACTIVITY",
  /** 参加会议 */
  MEETING = "MEETING",
  /** 参加年度重点工作 */
  KEY_WORK = "KEY_WORK",
  /** 营商环境问题报送 */
  ENVIRONMENT = "ENVIRONMENT",
  /** 意见征集汇总报送 */
  OPINION = "OPINION",
  /** 办好事、解难题、做公益情况 */
  CHARITY = "CHARITY",
}

/**
 * 履职考核项目类型字典数据
 */
export const AssessmentItemTypeDicts: Dict<AssessmentItemTypeEnum>[] = [
  { value: AssessmentItemTypeEnum.ACTIVITY, label: "参加活动", sort: 1 },
  { value: AssessmentItemTypeEnum.MEETING, label: "参加会议", sort: 2 },
  { value: AssessmentItemTypeEnum.KEY_WORK, label: "年度重点工作", sort: 3 },
  { value: AssessmentItemTypeEnum.ENVIRONMENT, label: "营商环境问题报送", sort: 4 },
  { value: AssessmentItemTypeEnum.OPINION, label: "意见建议提交", sort: 5 },
  { value: AssessmentItemTypeEnum.CHARITY, label: "办好事、解难题、做公益", sort: 6 },
];

/**
 * 履职考核项目类型字典数据Map
 */
export const AssessmentItemTypeDictMap = dictsToMap(AssessmentItemTypeDicts);
