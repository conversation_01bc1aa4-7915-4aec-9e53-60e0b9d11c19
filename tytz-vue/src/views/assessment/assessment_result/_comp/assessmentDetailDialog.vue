<template>
  <div>
    <el-dialog v-model="visiable" :title="dialogTitleText" width="999">
      <div class="mb-10px text-15px color-#000">{{ totalScoreText }}</div>
      <SearchPage
        :service="searchPageService"
        :form-items="searchPageFormItems"
        :table-columns="tableColumns"
        table-cell-empty-text="-"
      ></SearchPage>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import SearchPage from "@/components/SearchPage/index.vue";
import { AssessmentScoreVO } from "@/api/assessment/types/score";
import { FormItemsUnionTypeList } from "@/components/Form/type";
import { AssessmentItemTypeEnum } from "@/views/assessment/configs/AssessmentItemTypeEnum";
import { getAssessmentScoreDetailDataPageApi } from "@/api/assessment/score";

interface IColumnsLabelMapV {
  scoreField: keyof AssessmentScoreVO;
  dialogTitle: string;
  dataTitle: string;
  dataType: string;
}

/** 组件需要的props */
const props = withDefaults(
  defineProps<{
    assessmentResult: AssessmentScoreVO;
    assessmentItemType: AssessmentItemTypeEnum;
  }>(),
  {
    assessmentResult: () => ({}),
  }
);

/** 显示控制 */
const visiable = defineModel("visiable", {
  type: Boolean,
  required: true,
  default: false,
});

/** 详情表格列label配置 */
const columnsLabelMap: Record<AssessmentItemTypeEnum, IColumnsLabelMapV> = {
  [AssessmentItemTypeEnum.ACTIVITY]: {
    scoreField: "activityScore",
    dialogTitle: "参加活动得分详情",
    dataTitle: "活动名称",
    dataType: "活动类型",
  },
  [AssessmentItemTypeEnum.MEETING]: {
    scoreField: "meetingScore",
    dialogTitle: "参加会议得分详情",
    dataTitle: "会议名称",
    dataType: "会议类型",
  },
  [AssessmentItemTypeEnum.KEY_WORK]: {
    scoreField: "keyWorkScore",
    dialogTitle: "年度重点工作得分详情",
    dataTitle: "重点工作名称",
    dataType: "重点工作类型",
  },
  [AssessmentItemTypeEnum.ENVIRONMENT]: {
    scoreField: "environmentScore",
    dialogTitle: "营商环境问题报送得分详情",
    dataTitle: "营商环境问题标题",
    dataType: "营商环境问题类别",
  },
  [AssessmentItemTypeEnum.OPINION]: {
    scoreField: "opinionScore",
    dialogTitle: "意见建议得分详情",
    dataTitle: "意见建议内容",
    dataType: "意见建议类型",
  },
  [AssessmentItemTypeEnum.CHARITY]: {
    scoreField: "activityScore",
    dialogTitle: "公益慈善活动得分详情",
    dataTitle: "活动名称",
    dataType: "活动类型",
  },
};

/** dialog 的title显示 */
const dialogTitleText = computed(() => {
  const { dialogTitle } = columnsLabelMap[props.assessmentItemType];
  return `${props.assessmentResult?.memberName}${dialogTitle || "履职得分详情"}(${props.assessmentResult?.year})`;
});

/** 项目总得分展示文本 */
const totalScoreText = computed(() => {
  const { scoreField } = columnsLabelMap[props.assessmentItemType];
  const totalScore = props.assessmentResult?.[scoreField] ?? "-";
  return `总得分：${totalScore}`;
});

/** 展示的表格列数据 */
const tableColumns = computed(() => {
  const { dataTitle = "数据标题", dataType = "数据类型" } =
    columnsLabelMap[props.assessmentItemType];

  return {
    dataTitle: dataTitle,
    dataTypeLabel: dataType,
    score: "获得评分",
  };
});

/** SearchPage 的 service */
const searchPageService = async (params: any) => {
  return getAssessmentScoreDetailDataPageApi({
    ...params,
    dataType: props.assessmentItemType,
    category: props.assessmentResult?.category,
    memberId: props.assessmentResult?.memberId,
    year: props.assessmentResult?.year,
  });
};

/** SerachPage 组件的 formItems */
const searchPageFormItems: FormItemsUnionTypeList = [];
</script>

<style scoped lang="scss"></style>
