import request from "@/utils/request";
import { AssessmentScoreDetailDataVO, AssessmentScoreVO } from "./types/score";

const API_PREFIX = "/api/v1/assessment-score";
const DETAIL_MODULE_API_PREFIX = "/api/v1/assessment-score-details";

/**
 * 获取履职考核规则列表
 */
export const getAssessmentTotalScorePageApi = (queryParams: any) => {
  return request<any, PageResult<AssessmentScoreVO[]>>({
    url: `${API_PREFIX}/page`,
    method: "get",
    params: queryParams,
  });
};

/**
 * 分页获取履职考核具体项目的得分与对应数据详情
 *
 * @param queryParams
 */
export const getAssessmentScoreDetailDataPageApi = (queryParams: any) => {
  return request<any, PageResult<AssessmentScoreDetailDataVO[]>>({
    url: `${DETAIL_MODULE_API_PREFIX}/detail-data/page`,
    method: "get",
    params: queryParams,
  });
};
