import { ActivityTypeEnum } from "@/views/rating_mgmt/configs/dicts/ActivityTypeDict";

/**
 * 履职考核得分总表视图对象
 *
 * AssessmentScoreVO
 */
export interface AssessmentScoreVO {
  /**
   * 参加活动次数
   */
  activityCount?: number;
  /**
   * 活动得分
   */
  activityScore?: number;
  /**
   * 得分归类
   */
  category?: AssessmentCategoryEnum;
  /**
   * 得分归类标签
   */
  categoryLabel?: string;
  /**
   * 所属单位（用户表company字段）
   */
  company?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 营商环境问题报送数量
   */
  environmentCount?: number;
  /**
   * 营商环境问题报送得分
   */
  environmentScore?: number;
  /**
   * 得分ID
   */
  id?: number;
  /**
   * 年度重点工作次数
   */
  keyWorkCount?: number;
  /**
   * 重点工作得分
   */
  keyWorkScore?: number;
  /**
   * 参加会议次数
   */
  meetingCount?: number;
  /**
   * 会议得分
   */
  meetingScore?: number;
  /**
   * 会员所属商会（用户部门）
   */
  memberDeptName?: string;
  /**
   * 被考核人用户ID
   */
  memberId?: number;
  /**
   * 会员名称（用户名）
   */
  memberName?: string;
  /**
   * 意见建议数量
   */
  opinionCount?: number;
  /**
   * 意见建议得分
   */
  opinionScore?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 总得分
   */
  score?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 年度
   */
  year?: number;
}

/**
 * 履职考核项目具体的得分与对应数据详情
 */
export interface AssessmentScoreDetailDataVO {
  /**
   * 得分对应数据的标题/名称
   */
  dataTitle: string;
  /**
   * 得分对应的数据ID
   */
  dataId: number;
  /**
   * 得分对应数类型翻译
   */
  dataTypeLabel: dataTypeLabel;
  /** 得分对应数据类型 */
  dataType:
    | ActivityTypeEnum
    | MeetingTypeEnum
    | KeyWorkTypeEnum
    | EnvironmentProblemTypeEnum
    | OpinionTypeEnum;
  /** 具体得分 */
  score: number;
}
