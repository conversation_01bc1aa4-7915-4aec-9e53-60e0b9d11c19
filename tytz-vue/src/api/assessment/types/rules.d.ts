/**
 * 履职考核规则视图对象
 *
 * AssessmentRulesPageVO
 */
export interface AssessmentRulesPageVO {
  /**
   * 履职项目类别
   */
  category: AssessmentCategoryEnum;
  /**
   * 履职项目类别标签
   */
  categoryLabel: string;
  /**
   * 规则ID
   */
  id: number;
  /** item 需要合并的行数 */
  itemSpan: numbner;
  /**
   * 履职项目编号
   */
  itemCode: string;
  /**
   * 履职项目名称
   */
  itemName: string;
  /**
   * 得分上限
   */
  limitMax: number;
  /**
   * 得分下限
   */
  limitMin: number;
  /**
   * 履职规则编号
   */
  ruleCode: string;
  /**
   * 履职规则名称
   */
  ruleName: string;
  /**
   * 得分
   */
  score: number;
  /**
   * 排序
   */
  sort: number;
}
